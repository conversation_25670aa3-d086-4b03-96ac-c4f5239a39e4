import React from 'react'
import { ChevronLeft, ChevronRight, Flag, RotateCcw } from 'lucide-react'
import { Test } from '../../types'
import { useTestStore } from '../../store/useTestStore'

interface NavigationPanelProps {
  test: Test
  currentPartIndex: number
  currentQuestionIndex: number
}

export const NavigationPanel: React.FC<NavigationPanelProps> = ({
  test,
  currentPartIndex,
  currentQuestionIndex
}) => {
  const { 
    setCurrentPart, 
    setCurrentQuestion, 
    flagQuestion, 
    unflagQuestion 
  } = useTestStore()

  const currentPart = test.parts[currentPartIndex]
  const currentQuestion = currentPart?.questions[currentQuestionIndex]
  const totalQuestions = currentPart?.questions.length || 0

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestion(currentQuestionIndex - 1)
    } else if (currentPartIndex > 0) {
      const previousPart = test.parts[currentPartIndex - 1]
      setCurrentPart(currentPartIndex - 1)
      setCurrentQuestion(previousPart.questions.length - 1)
    }
  }

  const handleNextQuestion = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestion(currentQuestionIndex + 1)
    } else if (currentPartIndex < test.parts.length - 1) {
      setCurrentPart(currentPartIndex + 1)
      setCurrentQuestion(0)
    }
  }

  const handleQuestionSelect = (questionIndex: number) => {
    setCurrentQuestion(questionIndex)
  }

  const handlePartSelect = (partIndex: number) => {
    setCurrentPart(partIndex)
    setCurrentQuestion(0)
  }

  const handleToggleFlag = () => {
    if (currentQuestion) {
      if (currentQuestion.isFlagged) {
        unflagQuestion(currentQuestion.id)
      } else {
        flagQuestion(currentQuestion.id)
      }
    }
  }

  const getQuestionStatus = (question: any) => {
    if (question.isFlagged) return 'flagged'
    if (question.isAttempted) return 'attempted'
    return 'not-attempted'
  }

  const getAttemptedCount = (partIndex: number) => {
    return test.parts[partIndex].questions.filter(q => q.isAttempted).length
  }

  return (
    <div className="bg-white border-t-2 border-cambridge-gray-200">
      {/* Parts Navigation */}
      <div className="px-6 py-3 border-b border-cambridge-gray-200">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-cambridge-gray-700">Test Parts</h3>
          <span className="text-xs text-cambridge-gray-500">
            Part {currentPartIndex + 1} of {test.parts.length}
          </span>
        </div>
        
        <div className="flex space-x-2 overflow-x-auto">
          {test.parts.map((part, index) => {
            const attemptedCount = getAttemptedCount(index)
            const totalCount = part.questions.length
            const isActive = index === currentPartIndex
            
            return (
              <button
                key={part.partNumber}
                onClick={() => handlePartSelect(index)}
                className={`flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-cambridge-blue text-white'
                    : 'bg-cambridge-gray-100 text-cambridge-gray-700 hover:bg-cambridge-gray-200'
                }`}
              >
                <div className="text-center">
                  <div>Part {part.partNumber}</div>
                  <div className="text-xs opacity-75">
                    {attemptedCount} of {totalCount}
                  </div>
                </div>
              </button>
            )
          })}
        </div>
      </div>

      {/* Questions Navigation */}
      <div className="px-6 py-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-4">
            <h4 className="text-sm font-medium text-cambridge-gray-700">
              {currentPart?.title}
            </h4>
            <span className="text-xs text-cambridge-gray-500">
              Question {currentQuestionIndex + 1} of {totalQuestions}
            </span>
          </div>

          {/* Question Controls */}
          <div className="flex items-center space-x-2">
            {/* Flag Button */}
            <button
              onClick={handleToggleFlag}
              className={`p-2 rounded-lg transition-colors ${
                currentQuestion?.isFlagged
                  ? 'bg-cambridge-red text-white'
                  : 'bg-cambridge-gray-100 text-cambridge-gray-600 hover:bg-cambridge-gray-200'
              }`}
              title={currentQuestion?.isFlagged ? 'Remove flag' : 'Flag for review'}
            >
              <Flag className="h-4 w-4" />
            </button>

            {/* Previous Button */}
            <button
              onClick={handlePreviousQuestion}
              disabled={currentPartIndex === 0 && currentQuestionIndex === 0}
              className="p-2 rounded-lg bg-cambridge-gray-100 text-cambridge-gray-600 hover:bg-cambridge-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            {/* Next Button */}
            <button
              onClick={handleNextQuestion}
              disabled={
                currentPartIndex === test.parts.length - 1 && 
                currentQuestionIndex === totalQuestions - 1
              }
              className="p-2 rounded-lg bg-cambridge-gray-100 text-cambridge-gray-600 hover:bg-cambridge-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Question Numbers */}
        <div className="flex flex-wrap gap-2">
          {currentPart?.questions.map((question, index) => {
            const status = getQuestionStatus(question)
            const isActive = index === currentQuestionIndex
            
            return (
              <button
                key={question.id}
                onClick={() => handleQuestionSelect(index)}
                className={`w-10 h-10 rounded-lg text-sm font-medium transition-colors relative ${
                  isActive
                    ? 'bg-cambridge-blue text-white'
                    : status === 'attempted'
                    ? 'bg-cambridge-green text-white'
                    : status === 'flagged'
                    ? 'bg-cambridge-red text-white'
                    : 'bg-cambridge-gray-100 text-cambridge-gray-700 hover:bg-cambridge-gray-200'
                }`}
              >
                {question.questionNumber}
                {question.isFlagged && (
                  <Flag className="h-3 w-3 absolute -top-1 -right-1 text-cambridge-red" />
                )}
              </button>
            )
          })}
        </div>

        {/* Status Legend */}
        <div className="flex items-center justify-center space-x-6 mt-4 text-xs text-cambridge-gray-600">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-cambridge-blue rounded"></div>
            <span>Active</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-cambridge-green rounded"></div>
            <span>Attempted</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-cambridge-gray-300 rounded"></div>
            <span>Not attempted</span>
          </div>
          <div className="flex items-center space-x-2">
            <Flag className="h-3 w-3 text-cambridge-red" />
            <span>Flagged</span>
          </div>
        </div>

        {/* Review Button */}
        <div className="flex justify-center mt-4">
          <button className="btn-secondary flex items-center space-x-2">
            <RotateCcw className="h-4 w-4" />
            <span>Review your answers</span>
          </button>
        </div>
      </div>
    </div>
  )
}
