# B2 First Trainer - AI-Powered Cambridge Exam Preparation

A comprehensive web application for Cambridge B2 First exam preparation, featuring AI-generated exercises powered by Google Gemini and an authentic Inspera-like interface.

## 🚀 Features

### ✅ Implemented (Phase 1)
- **Inspera-like Interface**: Authentic Cambridge Digital B2 platform design
- **Test Selection**: Choose specific parts to practice or take full tests
- **Reading & Use of English**: All 7 parts implemented
  - Part 1: Multiple Choice Cloze
  - Part 2: Open Cloze  
  - Part 3: Word Formation
  - Part 4: Key Word Transformation (basic)
  - Part 5-7: Placeholder implementations
- **Writing**: Essay questions with word count tracking
- **Timer System**: Exam mode with precise time limits or practice mode
- **Progress Tracking**: Visual indicators for attempted/flagged questions
- **AI Integration**: Google Gemini API for dynamic content generation
- **Responsive Design**: Modern, accessible interface

### 🔄 In Development (Phase 2)
- Enhanced AI prompt engineering for better question quality
- Listening module with audio generation
- Advanced Writing assessment with detailed AI feedback
- Speaking module with recording capabilities

### 📋 Planned (Phase 3)
- User authentication and progress persistence
- Detailed analytics and performance tracking
- Personalized study recommendations
- Mobile app (PWA)

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Zustand** for state management
- **React Router** for navigation
- **Lucide React** for icons

### Backend
- **Node.js** with Express
- **Google Gemini API** for AI content generation
- **CORS** enabled for cross-origin requests

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ and npm
- Google Gemini API key

### 1. Clone the Repository
```bash
git clone <repository-url>
cd B2
```

### 2. Install Frontend Dependencies
```bash
npm install
```

### 3. Install Backend Dependencies
```bash
cd server
npm install
```

### 4. Environment Configuration
Create `server/.env` file:
```env
PORT=3001
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.5-flash-preview-05-20
NODE_ENV=development
```

### 5. Start the Application

**Terminal 1 - Backend Server:**
```bash
cd server
npm run dev
```

**Terminal 2 - Frontend Development Server:**
```bash
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

## 🎯 Usage

### Getting Started
1. Open the application in your browser
2. Choose from the dashboard:
   - **Take Full Test**: Complete exam with all parts
   - **Practice Mode**: No time limits, focus on learning
   - **Practice Parts**: Select specific parts to work on

### Test Modes
- **Exam Mode**: Realistic time limits, automatic submission
- **Practice Mode**: No time pressure, immediate feedback

### Navigation
- Use the bottom navigation panel to move between parts and questions
- Flag questions for review using the flag button
- Track progress with visual indicators (attempted/not attempted/flagged)

## 🤖 AI Integration

The application uses Google Gemini AI to:
- Generate dynamic test questions based on Cambridge B2 standards
- Create authentic reading passages and exercises
- Provide detailed feedback and explanations
- Assess writing tasks with detailed criteria

### API Endpoints
- `POST /api/generate-task` - Generate new test questions
- `POST /api/assess-writing` - Assess writing submissions
- `POST /api/get-explanation` - Get explanations for answers

## 🎨 Design Philosophy

The interface closely mimics the official Cambridge Inspera platform:
- Clean, minimalist design
- Cambridge brand colors (blue, green, orange, purple)
- Authentic question layouts and navigation
- Professional typography and spacing
- Accessibility-focused design

## 📁 Project Structure

```
B2/
├── src/                          # Frontend source code
│   ├── components/              # React components
│   │   ├── test/               # Test-specific components
│   │   │   ├── questions/      # Question type components
│   │   │   ├── Header.tsx      # Test header
│   │   │   ├── NavigationPanel.tsx
│   │   │   ├── QuestionArea.tsx
│   │   │   └── Timer.tsx
│   │   ├── Dashboard.tsx       # Main dashboard
│   │   ├── TestLayout.tsx      # Test container
│   │   └── TestSelection.tsx   # Test selection screen
│   ├── services/               # API services
│   ├── store/                  # State management
│   ├── types/                  # TypeScript definitions
│   └── App.tsx                 # Main app component
├── server/                      # Backend server
│   ├── services/               # Business logic
│   │   └── geminiService.js    # Gemini AI integration
│   └── index.js                # Express server
├── public/                     # Static assets
└── package.json               # Dependencies
```

## 🔧 Configuration

### Customizing Time Limits
Edit `src/types/index.ts` to modify test time limits:
```typescript
export const TIME_LIMITS = {
  'reading-use-of-english': {
    total: 75,
    parts: { 1: 10, 2: 8, ... }
  }
}
```

### Adding New Question Types
1. Create component in `src/components/test/questions/`
2. Add type definition in `src/types/index.ts`
3. Update `QuestionArea.tsx` to render new type
4. Add generation logic in `server/services/geminiService.js`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is for educational purposes. Cambridge English and B2 First are trademarks of Cambridge Assessment English.

## 🆘 Support

For issues and questions:
1. Check the existing issues
2. Create a new issue with detailed description
3. Include steps to reproduce any bugs

---

**Note**: This application is designed for exam preparation and practice. It is not affiliated with Cambridge Assessment English.
