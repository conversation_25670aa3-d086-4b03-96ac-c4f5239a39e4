import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { BookOpen, PenTool, Headphones, Mic, Clock, Target, TrendingUp } from 'lucide-react'

export const Dashboard: React.FC = () => {
  const testTypes = [
    {
      id: 'reading-use-of-english',
      title: 'Reading & Use of English',
      description: '7 parts • 75 minutes • Grammar, vocabulary, and reading comprehension',
      icon: BookOpen,
      color: 'bg-cambridge-blue',
      parts: [
        'Part 1: Multiple Choice Cloze',
        'Part 2: Open Cloze',
        'Part 3: Word Formation',
        'Part 4: Key Word Transformation',
        'Part 5: Multiple Choice Reading',
        'Part 6: Gapped Text',
        'Part 7: Multiple Matching'
      ]
    },
    {
      id: 'writing',
      title: 'Writing',
      description: '2 parts • 80 minutes • Essay and creative writing tasks',
      icon: PenTool,
      color: 'bg-cambridge-green',
      parts: [
        'Part 1: Essay (compulsory)',
        'Part 2: Article/Email/Letter/Report/Review'
      ]
    },
    {
      id: 'listening',
      title: 'Listening',
      description: '4 parts • 40 minutes • Audio comprehension and note-taking',
      icon: Headphones,
      color: 'bg-cambridge-orange',
      parts: [
        'Part 1: Multiple Choice',
        'Part 2: Sentence Completion',
        'Part 3: Multiple Matching',
        'Part 4: Multiple Choice'
      ]
    },
    {
      id: 'speaking',
      title: 'Speaking',
      description: '4 parts • 14 minutes • Interactive conversation and presentation',
      icon: Mic,
      color: 'bg-cambridge-purple',
      parts: [
        'Part 1: Interview',
        'Part 2: Long Turn',
        'Part 3: Collaborative Task',
        'Part 4: Discussion'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-cambridge-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-cambridge-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Target className="h-8 w-8 text-cambridge-blue" />
                <h1 className="text-xl font-bold text-cambridge-gray-700">
                  B2 First Trainer
                </h1>
              </div>
              <span className="text-sm text-cambridge-gray-500 bg-cambridge-gray-100 px-2 py-1 rounded">
                AI-Powered
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <button className="btn-secondary">
                Settings
              </button>
              <button className="btn-primary">
                View Progress
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-cambridge-gray-700 mb-2">
            Welcome to B2 First Trainer
          </h2>
          <p className="text-lg text-cambridge-gray-600 mb-6">
            Practice for your Cambridge B2 First exam with AI-generated exercises that adapt to your level.
            Choose a specific part to practice or take a full test.
          </p>
          
          {/* Quick Actions */}
          <div className="flex flex-wrap gap-4 mb-8">
            <Link 
              to="/select?mode=full-test" 
              className="btn-primary flex items-center space-x-2"
            >
              <Clock className="h-5 w-5" />
              <span>Take Full Test</span>
            </Link>
            <Link 
              to="/select?mode=practice" 
              className="btn-secondary flex items-center space-x-2"
            >
              <Target className="h-5 w-5" />
              <span>Practice Mode</span>
            </Link>
            <button className="btn-secondary flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>View Statistics</span>
            </button>
          </div>
        </div>

        {/* Test Types Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {testTypes.map((testType) => {
            const IconComponent = testType.icon
            return (
              <div key={testType.id} className="card hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start space-x-4">
                  <div className={`${testType.color} p-3 rounded-lg`}>
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-cambridge-gray-700 mb-2">
                      {testType.title}
                    </h3>
                    <p className="text-cambridge-gray-600 mb-4">
                      {testType.description}
                    </p>
                    
                    {/* Parts List */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-cambridge-gray-700 mb-2">
                        Test Parts:
                      </h4>
                      <ul className="text-sm text-cambridge-gray-600 space-y-1">
                        {testType.parts.map((part, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <span className="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                            <span>{part}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-wrap gap-2">
                      <Link 
                        to={`/select?type=${testType.id}&mode=full`}
                        className="btn-primary text-sm"
                      >
                        Full Test
                      </Link>
                      <Link 
                        to={`/select?type=${testType.id}&mode=parts`}
                        className="btn-secondary text-sm"
                      >
                        Practice Parts
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Features Section */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6">
            <div className="bg-cambridge-blue p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
              <Target className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-cambridge-gray-700 mb-2">
              AI-Generated Content
            </h3>
            <p className="text-cambridge-gray-600">
              Dynamic exercises created by Google Gemini AI, ensuring fresh content every time you practice.
            </p>
          </div>
          
          <div className="text-center p-6">
            <div className="bg-cambridge-green p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
              <Clock className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-cambridge-gray-700 mb-2">
              Realistic Timing
            </h3>
            <p className="text-cambridge-gray-600">
              Practice with exact time limits from the real exam, or use practice mode without time pressure.
            </p>
          </div>
          
          <div className="text-center p-6">
            <div className="bg-cambridge-orange p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-cambridge-gray-700 mb-2">
              Detailed Feedback
            </h3>
            <p className="text-cambridge-gray-600">
              Get instant AI-powered feedback with explanations and tips to improve your performance.
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}
