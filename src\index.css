@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-white text-cambridge-gray-700 antialiased;
  }
}

@layer components {
  .btn-primary {
    @apply bg-cambridge-blue text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium;
  }
  
  .btn-secondary {
    @apply bg-cambridge-gray-100 text-cambridge-gray-700 px-4 py-2 rounded-md hover:bg-cambridge-gray-200 transition-colors duration-200 font-medium;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-cambridge-gray-200 p-6;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-cambridge-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cambridge-blue focus:border-transparent;
  }
  
  .radio-option {
    @apply flex items-center space-x-3 p-3 border-2 border-transparent rounded-lg cursor-pointer hover:bg-cambridge-gray-50 transition-colors duration-200;
  }
  
  .radio-option.selected {
    @apply border-cambridge-blue bg-blue-50;
  }
  
  .radio-option.option-a {
    @apply hover:border-cambridge-blue;
  }
  
  .radio-option.option-b {
    @apply hover:border-cambridge-purple;
  }
  
  .radio-option.option-c {
    @apply hover:border-cambridge-orange;
  }
  
  .radio-option.option-d {
    @apply hover:border-cambridge-green;
  }
}
