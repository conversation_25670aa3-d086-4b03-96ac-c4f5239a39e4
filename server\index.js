import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'
import { generateTask, assessWriting } from './services/geminiService.js'

dotenv.config()

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json())

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'B2 First Trainer API is running' })
})

// Generate task endpoint
app.post('/api/generate-task', async (req, res) => {
  try {
    const { taskType, topic, difficultyLevel, partNumber } = req.body

    if (!taskType || !partNumber) {
      return res.status(400).json({
        success: false,
        error: 'taskType and partNumber are required'
      })
    }

    console.log(`Generating task: ${taskType}, Part ${partNumber}, Topic: ${topic || 'general'}`)

    const task = await generateTask({
      taskType,
      topic: topic || 'general topics',
      difficultyLevel: difficultyLevel || 'B2',
      partNumber
    })

    res.json({
      success: true,
      task
    })

  } catch (error) {
    console.error('Error generating task:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to generate task'
    })
  }
})

// Assess writing endpoint
app.post('/api/assess-writing', async (req, res) => {
  try {
    const { taskType, originalPrompt, studentText, wordCount } = req.body

    if (!taskType || !originalPrompt || !studentText) {
      return res.status(400).json({
        success: false,
        error: 'taskType, originalPrompt, and studentText are required'
      })
    }

    console.log(`Assessing writing: ${taskType}, ${wordCount} words`)

    const assessment = await assessWriting({
      taskType,
      originalPrompt,
      studentText,
      studentWordCount: wordCount || studentText.split(/\s+/).length
    })

    res.json({
      success: true,
      assessment
    })

  } catch (error) {
    console.error('Error assessing writing:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to assess writing'
    })
  }
})

// Get explanation endpoint
app.post('/api/get-explanation', async (req, res) => {
  try {
    const { questionText, userAnswer, correctAnswer, context } = req.body

    if (!questionText || !correctAnswer) {
      return res.status(400).json({
        success: false,
        error: 'questionText and correctAnswer are required'
      })
    }

    // For now, return a mock explanation
    // In a real implementation, this would call Gemini API
    const explanation = `The correct answer is "${correctAnswer}". ${userAnswer ? `You answered "${userAnswer}". ` : ''}This is because the context requires a specific grammatical structure or vocabulary choice that fits the B2 level requirements.`

    res.json({
      success: true,
      explanation
    })

  } catch (error) {
    console.error('Error generating explanation:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to generate explanation'
    })
  }
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({
    success: false,
    error: 'Something went wrong!'
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  })
})

app.listen(PORT, () => {
  console.log(`🚀 B2 First Trainer API server running on port ${PORT}`)
  console.log(`📚 Environment: ${process.env.NODE_ENV}`)
  console.log(`🤖 Gemini Model: ${process.env.GEMINI_MODEL}`)
})
