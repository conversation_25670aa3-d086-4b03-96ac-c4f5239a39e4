import React, { useState } from 'react'
import { Link, useSearchParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, Clock, Play, Settings } from 'lucide-react'
import { useTestStore } from '../store/useTestStore'

export const TestSelection: React.FC = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { userSettings, updateUserSettings } = useTestStore()
  
  const testType = searchParams.get('type') || 'reading-use-of-english'
  const mode = searchParams.get('mode') || 'parts'
  
  const [selectedParts, setSelectedParts] = useState<number[]>([])
  const [practiceMode, setPracticeMode] = useState(userSettings.practiceMode)

  const testConfig = {
    'reading-use-of-english': {
      title: 'Reading & Use of English',
      totalTime: 75,
      parts: [
        { number: 1, title: 'Multiple Choice Cloze', time: 10, questions: 8 },
        { number: 2, title: 'Open Cloze', time: 8, questions: 8 },
        { number: 3, title: 'Word Formation', time: 8, questions: 8 },
        { number: 4, title: 'Key Word Transformation', time: 10, questions: 6 },
        { number: 5, title: 'Multiple Choice Reading', time: 15, questions: 6 },
        { number: 6, title: 'Gapped Text', time: 12, questions: 6 },
        { number: 7, title: 'Multiple Matching', time: 12, questions: 10 }
      ]
    },
    'writing': {
      title: 'Writing',
      totalTime: 80,
      parts: [
        { number: 1, title: 'Essay (Compulsory)', time: 40, questions: 1 },
        { number: 2, title: 'Article/Email/Letter/Report/Review', time: 40, questions: 1 }
      ]
    },
    'listening': {
      title: 'Listening',
      totalTime: 40,
      parts: [
        { number: 1, title: 'Multiple Choice', time: 8, questions: 8 },
        { number: 2, title: 'Sentence Completion', time: 10, questions: 10 },
        { number: 3, title: 'Multiple Matching', time: 8, questions: 5 },
        { number: 4, title: 'Multiple Choice', time: 14, questions: 7 }
      ]
    },
    'speaking': {
      title: 'Speaking',
      totalTime: 14,
      parts: [
        { number: 1, title: 'Interview', time: 2, questions: 3 },
        { number: 2, title: 'Long Turn', time: 4, questions: 2 },
        { number: 3, title: 'Collaborative Task', time: 4, questions: 1 },
        { number: 4, title: 'Discussion', time: 4, questions: 3 }
      ]
    }
  }

  const currentTest = testConfig[testType as keyof typeof testConfig]

  const handlePartToggle = (partNumber: number) => {
    setSelectedParts(prev => 
      prev.includes(partNumber) 
        ? prev.filter(p => p !== partNumber)
        : [...prev, partNumber]
    )
  }

  const handleStartTest = () => {
    updateUserSettings({ practiceMode })
    
    if (mode === 'full') {
      navigate(`/test/${testType}`)
    } else if (selectedParts.length > 0) {
      const partsQuery = selectedParts.join(',')
      navigate(`/test/${testType}?parts=${partsQuery}`)
    }
  }

  const getTotalTime = () => {
    if (mode === 'full') {
      return currentTest.totalTime
    }
    return selectedParts.reduce((total, partNum) => {
      const part = currentTest.parts.find(p => p.number === partNum)
      return total + (part?.time || 0)
    }, 0)
  }

  const getTotalQuestions = () => {
    if (mode === 'full') {
      return currentTest.parts.reduce((total, part) => total + part.questions, 0)
    }
    return selectedParts.reduce((total, partNum) => {
      const part = currentTest.parts.find(p => p.number === partNum)
      return total + (part?.questions || 0)
    }, 0)
  }

  return (
    <div className="min-h-screen bg-cambridge-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-cambridge-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link to="/" className="flex items-center space-x-2 text-cambridge-gray-600 hover:text-cambridge-gray-700">
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Dashboard</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-cambridge-gray-700 mb-2">
            {currentTest.title}
          </h1>
          <p className="text-lg text-cambridge-gray-600">
            {mode === 'full' 
              ? 'Complete the full test with all parts'
              : 'Select the parts you want to practice'
            }
          </p>
        </div>

        {/* Test Mode Settings */}
        <div className="card mb-6">
          <h3 className="text-lg font-semibold text-cambridge-gray-700 mb-4 flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Test Settings</span>
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-cambridge-gray-700">Practice Mode</label>
                <p className="text-sm text-cambridge-gray-600">
                  {practiceMode ? 'No time limit, focus on learning' : 'Exam conditions with time limits'}
                </p>
              </div>
              <button
                onClick={() => setPracticeMode(!practiceMode)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  practiceMode ? 'bg-cambridge-blue' : 'bg-cambridge-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    practiceMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Parts Selection */}
        {mode === 'parts' && (
          <div className="card mb-6">
            <h3 className="text-lg font-semibold text-cambridge-gray-700 mb-4">
              Select Parts to Practice
            </h3>
            
            <div className="space-y-3">
              {currentTest.parts.map((part) => (
                <div
                  key={part.number}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                    selectedParts.includes(part.number)
                      ? 'border-cambridge-blue bg-blue-50'
                      : 'border-cambridge-gray-200 hover:border-cambridge-gray-300'
                  }`}
                  onClick={() => handlePartToggle(part.number)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedParts.includes(part.number)}
                        onChange={() => handlePartToggle(part.number)}
                        className="h-4 w-4 text-cambridge-blue focus:ring-cambridge-blue border-cambridge-gray-300 rounded"
                      />
                      <div>
                        <h4 className="font-medium text-cambridge-gray-700">
                          Part {part.number}: {part.title}
                        </h4>
                        <p className="text-sm text-cambridge-gray-600">
                          {part.questions} questions • {part.time} minutes
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-cambridge-gray-500">
                      <Clock className="h-4 w-4" />
                      <span>{part.time}min</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Test Summary */}
        <div className="card mb-6">
          <h3 className="text-lg font-semibold text-cambridge-gray-700 mb-4">
            Test Summary
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-cambridge-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-cambridge-blue">{getTotalQuestions()}</div>
              <div className="text-sm text-cambridge-gray-600">Questions</div>
            </div>
            <div className="text-center p-4 bg-cambridge-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-cambridge-green">{getTotalTime()}</div>
              <div className="text-sm text-cambridge-gray-600">Minutes</div>
            </div>
            <div className="text-center p-4 bg-cambridge-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-cambridge-orange">
                {mode === 'full' ? currentTest.parts.length : selectedParts.length}
              </div>
              <div className="text-sm text-cambridge-gray-600">Parts</div>
            </div>
            <div className="text-center p-4 bg-cambridge-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-cambridge-purple">
                {practiceMode ? 'Practice' : 'Exam'}
              </div>
              <div className="text-sm text-cambridge-gray-600">Mode</div>
            </div>
          </div>
        </div>

        {/* Start Button */}
        <div className="flex justify-center">
          <button
            onClick={handleStartTest}
            disabled={mode === 'parts' && selectedParts.length === 0}
            className={`btn-primary flex items-center space-x-2 text-lg px-8 py-3 ${
              mode === 'parts' && selectedParts.length === 0 
                ? 'opacity-50 cursor-not-allowed' 
                : ''
            }`}
          >
            <Play className="h-5 w-5" />
            <span>Start Test</span>
          </button>
        </div>

        {mode === 'parts' && selectedParts.length === 0 && (
          <p className="text-center text-cambridge-gray-500 mt-4">
            Please select at least one part to practice
          </p>
        )}
      </main>
    </div>
  )
}
