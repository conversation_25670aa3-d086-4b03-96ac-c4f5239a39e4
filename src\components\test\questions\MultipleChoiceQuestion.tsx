import React from 'react'
import { Question } from '../../../types'
import { useTestStore } from '../../../store/useTestStore'

interface MultipleChoiceQuestionProps {
  question: Question
}

export const MultipleChoiceQuestion: React.FC<MultipleChoiceQuestionProps> = ({
  question
}) => {
  const { updateUserAnswer } = useTestStore()

  const handleOptionSelect = (optionIndex: number) => {
    updateUserAnswer(question.id, optionIndex)
  }

  const getOptionClass = (index: number) => {
    const baseClass = "radio-option"
    const isSelected = question.userAnswer === index
    
    // Color classes based on option index (A, B, C, D)
    const colorClasses = [
      'option-a border-cambridge-blue',
      'option-b border-cambridge-purple', 
      'option-c border-cambridge-orange',
      'option-d border-cambridge-green'
    ]
    
    return `${baseClass} ${colorClasses[index]} ${isSelected ? 'selected' : ''}`
  }

  const getOptionLabel = (index: number) => {
    return String.fromCharCode(65 + index) // A, B, C, D
  }

  return (
    <div className="space-y-6">
      {/* Question Text */}
      <div className="prose max-w-none">
        <div className="text-cambridge-gray-700 leading-relaxed">
          {question.text.split('[GAP').map((part, index) => {
            if (index === 0) return part
            
            const gapMatch = part.match(/^(\s*\d+\])(.*)/)
            if (gapMatch) {
              const gapNumber = gapMatch[1]
              const remainingText = gapMatch[2]
              
              return (
                <span key={index}>
                  <span className="inline-block bg-cambridge-blue text-white px-2 py-1 rounded text-sm font-medium mx-1">
                    {gapNumber.replace(']', '')}
                  </span>
                  {remainingText}
                </span>
              )
            }
            return part
          })}
        </div>
      </div>

      {/* Answer Options */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-cambridge-gray-700 mb-3">
          Choose the correct answer:
        </h4>
        
        {question.options?.map((option, index) => (
          <div
            key={index}
            className={getOptionClass(index)}
            onClick={() => handleOptionSelect(index)}
          >
            <div className="flex items-center space-x-3">
              {/* Radio Button */}
              <div className="relative">
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={index}
                  checked={question.userAnswer === index}
                  onChange={() => handleOptionSelect(index)}
                  className="sr-only"
                />
                <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                  question.userAnswer === index
                    ? 'border-cambridge-blue bg-cambridge-blue'
                    : 'border-cambridge-gray-300'
                }`}>
                  {question.userAnswer === index && (
                    <div className="w-2 h-2 rounded-full bg-white"></div>
                  )}
                </div>
              </div>

              {/* Option Label and Text */}
              <div className="flex items-center space-x-3">
                <span className="font-medium text-cambridge-gray-700 min-w-[24px]">
                  {getOptionLabel(index)}
                </span>
                <span className="text-cambridge-gray-700">
                  {option}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Feedback Section (shown after answer is selected) */}
      {question.userAnswer !== undefined && question.explanation && (
        <div className="mt-6 p-4 bg-cambridge-gray-50 rounded-lg border border-cambridge-gray-200">
          <h5 className="text-sm font-medium text-cambridge-gray-700 mb-2">
            Explanation:
          </h5>
          <p className="text-sm text-cambridge-gray-600 leading-relaxed">
            {question.explanation}
          </p>
        </div>
      )}

      {/* Answer Status */}
      <div className="flex items-center justify-between pt-4 border-t border-cambridge-gray-200">
        <div className="flex items-center space-x-2">
          {question.userAnswer !== undefined ? (
            <>
              <div className="w-2 h-2 bg-cambridge-green rounded-full"></div>
              <span className="text-sm text-cambridge-green">Answer selected</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-cambridge-gray-400 rounded-full"></div>
              <span className="text-sm text-cambridge-gray-500">Not answered</span>
            </>
          )}
        </div>
        
        {question.userAnswer !== undefined && (
          <span className="text-sm text-cambridge-gray-600">
            Selected: {getOptionLabel(question.userAnswer as number)}
          </span>
        )}
      </div>
    </div>
  )
}
