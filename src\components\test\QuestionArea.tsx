import React from 'react'
import { TestPart, Question } from '../../types'
import { MultipleChoiceQuestion } from './questions/MultipleChoiceQuestion'
import { OpenClozeQuestion } from './questions/OpenClozeQuestion'
import { WordFormationQuestion } from './questions/WordFormationQuestion'
import { EssayQuestion } from './questions/EssayQuestion'

interface QuestionAreaProps {
  part: TestPart
  question: Question
  questionIndex: number
}

export const QuestionArea: React.FC<QuestionAreaProps> = ({
  part,
  question,
  questionIndex
}) => {
  const renderQuestion = () => {
    switch (question.type) {
      case 'multiple-choice-cloze':
      case 'multiple-choice-reading':
      case 'listening-multiple-choice':
        return <MultipleChoiceQuestion question={question} />
      
      case 'open-cloze':
        return <OpenClozeQuestion question={question} />
      
      case 'word-formation':
        return <WordFormationQuestion question={question} />
      
      case 'essay':
      case 'writing-task':
        return <EssayQuestion question={question} />
      
      default:
        return (
          <div className="text-center py-8">
            <p className="text-cambridge-gray-600">
              Question type "{question.type}" is not yet implemented.
            </p>
            <p className="text-sm text-cambridge-gray-500 mt-2">
              This feature will be available in a future update.
            </p>
          </div>
        )
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Part Instructions */}
      <div className="mb-8 p-6 bg-cambridge-gray-50 rounded-lg border border-cambridge-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-cambridge-gray-700">
            Part {part.partNumber}: {part.title}
          </h2>
          <span className="text-sm text-cambridge-gray-500 bg-white px-3 py-1 rounded-full">
            {part.timeLimit} minutes
          </span>
        </div>
        
        <p className="text-cambridge-gray-600 mb-4">
          {part.description}
        </p>
        
        <div className="bg-white p-4 rounded-lg border border-cambridge-gray-200">
          <h3 className="font-medium text-cambridge-gray-700 mb-2">Instructions:</h3>
          <p className="text-cambridge-gray-600 text-sm leading-relaxed">
            {part.instructions}
          </p>
        </div>
      </div>

      {/* Question Content */}
      <div className="bg-white rounded-lg border border-cambridge-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-cambridge-gray-700">
            Question {question.questionNumber}
          </h3>
          <div className="flex items-center space-x-2">
            {question.isFlagged && (
              <span className="text-xs bg-cambridge-red text-white px-2 py-1 rounded-full">
                Flagged
              </span>
            )}
            {question.isAttempted && (
              <span className="text-xs bg-cambridge-green text-white px-2 py-1 rounded-full">
                Attempted
              </span>
            )}
          </div>
        </div>

        {renderQuestion()}
      </div>

      {/* Question Progress */}
      <div className="mt-6 text-center">
        <div className="inline-flex items-center space-x-2 text-sm text-cambridge-gray-500">
          <span>Question {questionIndex + 1} of {part.questions.length}</span>
          <span>•</span>
          <span>Part {part.partNumber}</span>
        </div>
      </div>
    </div>
  )
}
