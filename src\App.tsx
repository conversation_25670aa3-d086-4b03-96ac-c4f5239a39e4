import React from 'react'
import { <PERSON>rows<PERSON><PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import { Dashboard } from './components/Dashboard'
import { TestLayout } from './components/TestLayout'
import { TestSelection } from './components/TestSelection'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-cambridge-gray-50">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/select" element={<TestSelection />} />
          <Route path="/test/:testType" element={<TestLayout />} />
          <Route path="/test/:testType/:partNumber" element={<TestLayout />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
