import React, { useEffect, useState } from 'react'
import { useParams, useSearchParams, useNavigate } from 'react-router-dom'
import { Header } from './test/Header'
import { NavigationPanel } from './test/NavigationPanel'
import { QuestionArea } from './test/QuestionArea'
import { Timer } from './test/Timer'
import { useTestStore } from '../store/useTestStore'
import { generateMockTest } from '../services/testGenerator'
import { TestType } from '../types'

export const TestLayout: React.FC = () => {
  const { testType } = useParams<{ testType: string }>()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { 
    currentTest, 
    setCurrentTest, 
    currentPartIndex, 
    currentQuestionIndex,
    userSettings,
    isTimerRunning,
    startTimer
  } = useTestStore()

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const selectedParts = searchParams.get('parts')?.split(',').map(Number) || []

  useEffect(() => {
    const initializeTest = async () => {
      try {
        setIsLoading(true)
        setError(null)

        if (!testType) {
          throw new Error('Test type not specified')
        }

        // Generate test based on type and selected parts
        const test = await generateMockTest(
          testType as TestType, 
          selectedParts.length > 0 ? selectedParts : undefined
        )

        setCurrentTest(test)
        
        // Start timer if not in practice mode
        if (!userSettings.practiceMode) {
          startTimer()
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load test')
      } finally {
        setIsLoading(false)
      }
    }

    initializeTest()
  }, [testType, selectedParts, setCurrentTest, userSettings.practiceMode, startTimer])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-cambridge-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cambridge-blue mx-auto mb-4"></div>
          <p className="text-cambridge-gray-600">Generating your test...</p>
          <p className="text-sm text-cambridge-gray-500 mt-2">
            AI is creating personalized questions for you
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-cambridge-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
          <button 
            onClick={() => navigate('/')}
            className="btn-primary"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    )
  }

  if (!currentTest) {
    return (
      <div className="min-h-screen bg-cambridge-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-cambridge-gray-600">No test loaded</p>
          <button 
            onClick={() => navigate('/')}
            className="btn-primary mt-4"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    )
  }

  const currentPart = currentTest.parts[currentPartIndex]
  const currentQuestion = currentPart?.questions[currentQuestionIndex]

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header - Cambridge/Inspera style */}
      <Header 
        testTitle={currentTest.title}
        candidateId="B2-2024-001"
        isAudioPlaying={false}
        hasWifiConnection={true}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex">
        {/* Question Area - Main content */}
        <main className="flex-1 p-6 overflow-y-auto">
          {currentQuestion && currentPart ? (
            <QuestionArea 
              part={currentPart}
              question={currentQuestion}
              questionIndex={currentQuestionIndex}
            />
          ) : (
            <div className="text-center py-12">
              <p className="text-cambridge-gray-600">No questions available</p>
            </div>
          )}
        </main>

        {/* Timer Sidebar */}
        {!userSettings.practiceMode && (
          <aside className="w-64 bg-cambridge-gray-50 border-l border-cambridge-gray-200 p-4">
            <Timer />
          </aside>
        )}
      </div>

      {/* Navigation Panel - Bottom */}
      <NavigationPanel 
        test={currentTest}
        currentPartIndex={currentPartIndex}
        currentQuestionIndex={currentQuestionIndex}
      />
    </div>
  )
}
