<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B2 First Trainer Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">B2 First Trainer Functionality Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <button onclick="testPart(1)" class="bg-blue-500 text-white p-4 rounded-lg hover:bg-blue-600">
                Test Part 1<br><small>Multiple Choice Cloze</small>
            </button>
            <button onclick="testPart(2)" class="bg-green-500 text-white p-4 rounded-lg hover:bg-green-600">
                Test Part 2<br><small>Open Cloze</small>
            </button>
            <button onclick="testPart(3)" class="bg-purple-500 text-white p-4 rounded-lg hover:bg-purple-600">
                Test Part 3<br><small>Word Formation</small>
            </button>
            <button onclick="testPart(4)" class="bg-orange-500 text-white p-4 rounded-lg hover:bg-orange-600">
                Test Part 4<br><small>Key Word Transformations</small>
            </button>
            <button onclick="testPart(5)" class="bg-red-500 text-white p-4 rounded-lg hover:bg-red-600">
                Test Part 5<br><small>Multiple Choice Reading</small>
            </button>
            <button onclick="testPart(6)" class="bg-indigo-500 text-white p-4 rounded-lg hover:bg-indigo-600">
                Test Part 6<br><small>Gapped Text</small>
            </button>
            <button onclick="testPart(7)" class="bg-pink-500 text-white p-4 rounded-lg hover:bg-pink-600">
                Test Part 7<br><small>Multiple Matching</small>
            </button>
            <button onclick="testWriting()" class="bg-gray-500 text-white p-4 rounded-lg hover:bg-gray-600">
                Test Writing<br><small>Essay Task</small>
            </button>
        </div>

        <div class="bg-white rounded-lg p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">Test Results</h2>
            <div id="testResults" class="space-y-2">
                <p class="text-gray-600">Click buttons above to test each part...</p>
            </div>
        </div>

        <div class="bg-white rounded-lg p-6">
            <h2 class="text-xl font-bold mb-4">Generated Content Preview</h2>
            <div id="contentPreview" class="text-gray-600">
                No content generated yet...
            </div>
        </div>
    </div>

    <script>
        function addResult(partNumber, status, details) {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `p-3 rounded-lg ${status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            resultElement.innerHTML = `
                <strong>Part ${partNumber}:</strong> ${status === 'success' ? '✅ SUCCESS' : '❌ FAILED'}
                <br><small>${details}</small>
            `;
            resultsDiv.appendChild(resultElement);
        }

        function showContent(content) {
            const previewDiv = document.getElementById('contentPreview');
            previewDiv.innerHTML = `
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-bold mb-2">Generated Content:</h3>
                    <pre class="text-sm overflow-auto">${JSON.stringify(content, null, 2)}</pre>
                </div>
            `;
        }

        async function testPart(partNumber) {
            try {
                console.log(`Testing Part ${partNumber}...`);
                
                // Open the main application in a new window
                const mainWindow = window.open('demo-simple.html', '_blank');
                
                // Wait for the window to load
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Test if we can access the generation functions
                if (mainWindow.generateIntelligentContent) {
                    const requestId = `test_p${partNumber}_${Date.now()}`;
                    const content = await mainWindow.generateIntelligentContent('reading', partNumber, null, requestId);
                    
                    if (content && content.generationId) {
                        addResult(partNumber, 'success', `Generated content with ID: ${content.generationId.substring(0, 20)}...`);
                        showContent(content);
                    } else {
                        addResult(partNumber, 'failed', 'Content generation returned invalid data');
                    }
                } else {
                    addResult(partNumber, 'failed', 'Generation function not accessible');
                }
                
            } catch (error) {
                addResult(partNumber, 'failed', `Error: ${error.message}`);
                console.error(`Part ${partNumber} test failed:`, error);
            }
        }

        async function testWriting() {
            try {
                console.log('Testing Writing...');
                
                const mainWindow = window.open('demo-simple.html', '_blank');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                if (mainWindow.generateIntelligentContent) {
                    const requestId = `test_writing_${Date.now()}`;
                    const content = await mainWindow.generateIntelligentContent('writing', 1, null, requestId);
                    
                    if (content && content.generationId) {
                        addResult('Writing', 'success', `Generated essay prompt with ID: ${content.generationId.substring(0, 20)}...`);
                        showContent(content);
                    } else {
                        addResult('Writing', 'failed', 'Writing generation returned invalid data');
                    }
                } else {
                    addResult('Writing', 'failed', 'Generation function not accessible');
                }
                
            } catch (error) {
                addResult('Writing', 'failed', `Error: ${error.message}`);
                console.error('Writing test failed:', error);
            }
        }

        // Clear results on page load
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-600">Click buttons above to test each part...</p>';
        });
    </script>
</body>
</html>
