import { create } from 'zustand'
import { Test, Question, TestType, UserSettings } from '../types'

interface TestState {
  currentTest: Test | null
  currentPartIndex: number
  currentQuestionIndex: number
  userSettings: UserSettings
  isTimerRunning: boolean
  timeRemaining: number
  
  // Actions
  setCurrentTest: (test: Test) => void
  setCurrentPart: (partIndex: number) => void
  setCurrentQuestion: (questionIndex: number) => void
  updateUserAnswer: (questionId: string, answer: string | number) => void
  flagQuestion: (questionId: string) => void
  unflagQuestion: (questionId: string) => void
  startTimer: () => void
  stopTimer: () => void
  updateTimeRemaining: (time: number) => void
  updateUserSettings: (settings: Partial<UserSettings>) => void
  resetTest: () => void
}

export const useTestStore = create<TestState>((set, get) => ({
  currentTest: null,
  currentPartIndex: 0,
  currentQuestionIndex: 0,
  userSettings: {
    preferredTheme: 'light',
    soundEnabled: true,
    autoSave: true,
    practiceMode: false
  },
  isTimerRunning: false,
  timeRemaining: 0,

  setCurrentTest: (test: Test) => {
    set({ 
      currentTest: test,
      currentPartIndex: 0,
      currentQuestionIndex: 0,
      timeRemaining: test.totalTimeLimit * 60 // Convert to seconds
    })
  },

  setCurrentPart: (partIndex: number) => {
    set({ 
      currentPartIndex: partIndex,
      currentQuestionIndex: 0
    })
  },

  setCurrentQuestion: (questionIndex: number) => {
    set({ currentQuestionIndex: questionIndex })
  },

  updateUserAnswer: (questionId: string, answer: string | number) => {
    const { currentTest } = get()
    if (!currentTest) return

    const updatedTest = { ...currentTest }
    
    // Find and update the question
    for (const part of updatedTest.parts) {
      const question = part.questions.find(q => q.id === questionId)
      if (question) {
        question.userAnswer = answer
        question.isAttempted = true
        break
      }
    }

    set({ currentTest: updatedTest })
  },

  flagQuestion: (questionId: string) => {
    const { currentTest } = get()
    if (!currentTest) return

    const updatedTest = { ...currentTest }
    
    for (const part of updatedTest.parts) {
      const question = part.questions.find(q => q.id === questionId)
      if (question) {
        question.isFlagged = true
        break
      }
    }

    set({ currentTest: updatedTest })
  },

  unflagQuestion: (questionId: string) => {
    const { currentTest } = get()
    if (!currentTest) return

    const updatedTest = { ...currentTest }
    
    for (const part of updatedTest.parts) {
      const question = part.questions.find(q => q.id === questionId)
      if (question) {
        question.isFlagged = false
        break
      }
    }

    set({ currentTest: updatedTest })
  },

  startTimer: () => {
    set({ isTimerRunning: true })
  },

  stopTimer: () => {
    set({ isTimerRunning: false })
  },

  updateTimeRemaining: (time: number) => {
    set({ timeRemaining: time })
  },

  updateUserSettings: (settings: Partial<UserSettings>) => {
    const { userSettings } = get()
    set({ 
      userSettings: { ...userSettings, ...settings }
    })
  },

  resetTest: () => {
    set({
      currentTest: null,
      currentPartIndex: 0,
      currentQuestionIndex: 0,
      isTimerRunning: false,
      timeRemaining: 0
    })
  }
}))
