import axios from 'axios'
import dotenv from 'dotenv'

dotenv.config()

const GEMINI_API_KEY = process.env.GEMINI_API_KEY
const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20'
const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent`

// Sample Cambridge B2 test content for reference
const SAMPLE_CONTENT = {
  'multiple-choice-cloze': `
What is genealogy?

Genealogy is a branch of history. It concerns family history, [GAP 1] than the national or world history studied at school. It doesn't merely involve drawing a family tree, however – tracing your family history can also [GAP 2] in learning about your roots and your identity. The internet enables millions of people worldwide to [GAP 3] information about their family history, without great [GAP 4].

Question 1: [instead] [rather] [except] [sooner]
Question 2: [cause] [mean] [result] [lead]
Question 3: [accomplish] [access] [approach] [admit]
Question 4: [fee] [price] [charge] [expense]
`,
  'essay': `
Write an essay using all the notes and giving reasons for your point of view.

Every country in the world has problems with pollution and damage to the environment. Do you think these problems can be solved?

Notes
Write about:
1. transport
2. rivers and seas
3. .............................. (your own idea)
`
}

export const generateTask = async ({ taskType, topic, difficultyLevel, partNumber }) => {
  try {
    const prompt = createTaskGenerationPrompt(taskType, topic, difficultyLevel, partNumber)
    
    const response = await axios.post(
      `${GEMINI_API_URL}?key=${GEMINI_API_KEY}`,
      {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        }
      },
      {
        headers: {
          'Content-Type': 'application/json',
        }
      }
    )

    const generatedText = response.data.candidates[0].content.parts[0].text
    
    // Parse the JSON response from Gemini
    try {
      const taskData = JSON.parse(generatedText)
      return taskData
    } catch (parseError) {
      console.error('Failed to parse Gemini response as JSON:', parseError)
      // Return a fallback mock task
      return createMockTask(taskType, topic, partNumber)
    }

  } catch (error) {
    console.error('Error calling Gemini API:', error.response?.data || error.message)
    // Return a fallback mock task
    return createMockTask(taskType, topic, partNumber)
  }
}

export const assessWriting = async ({ taskType, originalPrompt, studentText, studentWordCount }) => {
  try {
    const prompt = createWritingAssessmentPrompt(taskType, originalPrompt, studentText, studentWordCount)
    
    const response = await axios.post(
      `${GEMINI_API_URL}?key=${GEMINI_API_KEY}`,
      {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      },
      {
        headers: {
          'Content-Type': 'application/json',
        }
      }
    )

    const generatedText = response.data.candidates[0].content.parts[0].text
    
    try {
      const assessment = JSON.parse(generatedText)
      return assessment
    } catch (parseError) {
      console.error('Failed to parse assessment response as JSON:', parseError)
      return createMockAssessment(studentWordCount)
    }

  } catch (error) {
    console.error('Error calling Gemini API for assessment:', error.response?.data || error.message)
    return createMockAssessment(studentWordCount)
  }
}

const createTaskGenerationPrompt = (taskType, topic, difficultyLevel, partNumber) => {
  const sampleContent = SAMPLE_CONTENT[taskType] || ''
  
  return `
Act as a Cambridge B2 First exam creator. Generate a ${taskType} test for Part ${partNumber}.

SPECIFICATIONS:
- Topic: ${topic}
- Level: ${difficultyLevel} (upper-intermediate)
- Part: ${partNumber}

EXAMPLE FORMAT AND STYLE REFERENCE:
---EXAMPLE START---
${sampleContent}
---EXAMPLE END---

REQUIREMENTS FOR NEW TASK:
1. Create an engaging text about: ${topic}
2. Follow the exact format of the example above
3. Ensure B2 level difficulty
4. Include proper answer options and correct answers
5. Provide brief explanations for correct answers

OUTPUT FORMAT (JSON):
{
  "text": "Full text with gaps marked as [GAP X]",
  "questions": [
    {
      "gap_number": 1,
      "options": ["option1", "option2", "option3", "option4"],
      "correct_option_index": 0,
      "explanation": "Brief explanation why this option is correct"
    }
  ]
}

Generate a NEW, original task now.
`
}

const createWritingAssessmentPrompt = (taskType, originalPrompt, studentText, studentWordCount) => {
  return `
Act as an experienced Cambridge B2 First writing examiner. Assess the following student's response.

TASK TYPE: ${taskType}
ORIGINAL PROMPT: ${originalPrompt}
STUDENT'S RESPONSE (word count: ${studentWordCount}):
---STUDENT TEXT START---
${studentText}
---STUDENT TEXT END---

Evaluate based on Cambridge B2 First assessment criteria:

1. Content (Score 0-5): Has the student addressed all parts adequately?
2. Communicative Achievement (Score 0-5): Does it achieve its purpose and use appropriate style?
3. Organisation (Score 0-5): Is it well-organized with clear paragraphs and cohesive devices?
4. Language (Score 0-5): Range and accuracy of vocabulary and grammar?

OUTPUT FORMAT (JSON):
{
  "assessment": {
    "content": {
      "score": YOUR_SCORE_0_TO_5,
      "comments": "Detailed comments with examples..."
    },
    "communicative_achievement": {
      "score": YOUR_SCORE_0_TO_5,
      "comments": "Detailed comments with examples..."
    },
    "organisation": {
      "score": YOUR_SCORE_0_TO_5,
      "comments": "Detailed comments with examples..."
    },
    "language": {
      "score": YOUR_SCORE_0_TO_5,
      "comments": "Detailed comments with examples..."
    }
  },
  "overall_feedback": "Summary and suggestions...",
  "estimated_band_score": "Strong B2 / Borderline B2/C1 / Needs improvement for B2"
}
`
}

const createMockTask = (taskType, topic, partNumber) => {
  // Fallback mock tasks when Gemini API is not available
  const mockTasks = {
    'multiple-choice-cloze': {
      text: `The rise of social media has fundamentally changed how we communicate. People can now [GAP 1] with friends and family across the globe instantly. However, this technological advancement has also [GAP 2] new challenges for society.`,
      questions: [
        {
          gap_number: 1,
          options: ["connect", "contact", "relate", "link"],
          correct_option_index: 0,
          explanation: "'Connect' is the most appropriate word for establishing communication or relationships."
        },
        {
          gap_number: 2,
          options: ["created", "made", "formed", "built"],
          correct_option_index: 0,
          explanation: "'Created' is correct as it means to bring something new into existence."
        }
      ]
    },
    'essay': {
      text: `Write an essay discussing the impact of technology on modern education. Consider both positive and negative aspects.

Notes to include:
1. Online learning opportunities
2. Digital divide and accessibility
3. Your own idea about technology in education

Write 140-190 words.`,
      questions: []
    }
  }

  return mockTasks[taskType] || {
    text: `Sample ${taskType} question about ${topic}`,
    questions: []
  }
}

const createMockAssessment = (wordCount) => {
  return {
    assessment: {
      content: {
        score: 4,
        comments: "Good coverage of the main points with relevant ideas and examples."
      },
      communicative_achievement: {
        score: 4,
        comments: "Appropriate style and register for the task. Clear communicative purpose."
      },
      organisation: {
        score: 3,
        comments: "Generally well-organized with clear paragraphs. Some linking words used effectively."
      },
      language: {
        score: 3,
        comments: "Good range of vocabulary and grammar structures with minor errors that don't impede communication."
      }
    },
    overall_feedback: "This is a solid B2 level response that addresses the task requirements well. Continue working on more sophisticated linking devices and varied sentence structures.",
    estimated_band_score: "Strong B2"
  }
}
