import React, { useEffect, useState } from 'react'
import { Clock, AlertTriangle, Pause, Play } from 'lucide-react'
import { useTestStore } from '../../store/useTestStore'

export const Timer: React.FC = () => {
  const { 
    timeRemaining, 
    isTimerRunning, 
    updateTimeRemaining, 
    startTimer, 
    stopTimer,
    currentTest 
  } = useTestStore()

  const [isPaused, setIsPaused] = useState(false)

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isTimerRunning && !isPaused && timeRemaining > 0) {
      interval = setInterval(() => {
        updateTimeRemaining(timeRemaining - 1)
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [isTimerRunning, isPaused, timeRemaining, updateTimeRemaining])

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getTimeStatus = () => {
    const totalTime = (currentTest?.totalTimeLimit || 0) * 60
    const percentage = (timeRemaining / totalTime) * 100

    if (percentage <= 10) return 'critical'
    if (percentage <= 25) return 'warning'
    return 'normal'
  }

  const timeStatus = getTimeStatus()

  const handleTogglePause = () => {
    if (isPaused) {
      setIsPaused(false)
      startTimer()
    } else {
      setIsPaused(true)
      stopTimer()
    }
  }

  return (
    <div className="space-y-4">
      {/* Timer Display */}
      <div className={`p-4 rounded-lg border-2 ${
        timeStatus === 'critical' 
          ? 'border-cambridge-red bg-red-50' 
          : timeStatus === 'warning'
          ? 'border-cambridge-orange bg-orange-50'
          : 'border-cambridge-gray-200 bg-white'
      }`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Clock className={`h-5 w-5 ${
              timeStatus === 'critical' 
                ? 'text-cambridge-red' 
                : timeStatus === 'warning'
                ? 'text-cambridge-orange'
                : 'text-cambridge-gray-600'
            }`} />
            <span className="text-sm font-medium text-cambridge-gray-700">
              Time Remaining
            </span>
          </div>
          
          {timeStatus === 'critical' && (
            <AlertTriangle className="h-5 w-5 text-cambridge-red" />
          )}
        </div>

        <div className={`text-2xl font-bold ${
          timeStatus === 'critical' 
            ? 'text-cambridge-red' 
            : timeStatus === 'warning'
            ? 'text-cambridge-orange'
            : 'text-cambridge-gray-700'
        }`}>
          {formatTime(timeRemaining)}
        </div>

        {/* Progress Bar */}
        <div className="mt-3">
          <div className="w-full bg-cambridge-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                timeStatus === 'critical' 
                  ? 'bg-cambridge-red' 
                  : timeStatus === 'warning'
                  ? 'bg-cambridge-orange'
                  : 'bg-cambridge-blue'
              }`}
              style={{ 
                width: `${Math.max(0, (timeRemaining / ((currentTest?.totalTimeLimit || 0) * 60)) * 100)}%` 
              }}
            />
          </div>
        </div>
      </div>

      {/* Timer Controls */}
      <div className="space-y-2">
        <button
          onClick={handleTogglePause}
          className={`w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
            isPaused 
              ? 'bg-cambridge-green text-white hover:bg-green-700'
              : 'bg-cambridge-orange text-white hover:bg-orange-700'
          }`}
        >
          {isPaused ? (
            <>
              <Play className="h-4 w-4" />
              <span>Resume</span>
            </>
          ) : (
            <>
              <Pause className="h-4 w-4" />
              <span>Pause</span>
            </>
          )}
        </button>
      </div>

      {/* Time Warnings */}
      {timeStatus === 'critical' && (
        <div className="p-3 bg-red-100 border border-cambridge-red rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-cambridge-red" />
            <span className="text-sm font-medium text-cambridge-red">
              Less than 10% time remaining!
            </span>
          </div>
          <p className="text-xs text-cambridge-red mt-1">
            Please review your answers and prepare to submit.
          </p>
        </div>
      )}

      {timeStatus === 'warning' && (
        <div className="p-3 bg-orange-100 border border-cambridge-orange rounded-lg">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-cambridge-orange" />
            <span className="text-sm font-medium text-cambridge-orange">
              25% time remaining
            </span>
          </div>
          <p className="text-xs text-cambridge-orange mt-1">
            Consider reviewing your answers soon.
          </p>
        </div>
      )}

      {/* Test Info */}
      <div className="p-3 bg-cambridge-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-cambridge-gray-700 mb-2">
          Test Information
        </h4>
        <div className="space-y-1 text-xs text-cambridge-gray-600">
          <div className="flex justify-between">
            <span>Total Time:</span>
            <span>{currentTest?.totalTimeLimit} minutes</span>
          </div>
          <div className="flex justify-between">
            <span>Parts:</span>
            <span>{currentTest?.parts.length}</span>
          </div>
          <div className="flex justify-between">
            <span>Status:</span>
            <span className={isPaused ? 'text-cambridge-orange' : 'text-cambridge-green'}>
              {isPaused ? 'Paused' : 'Running'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
