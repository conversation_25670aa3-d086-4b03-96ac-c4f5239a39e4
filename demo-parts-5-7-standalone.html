<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B2 First Trainer - Parts 5-7 AI Demo (Standalone)</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
                🤖 B2 First Trainer - Parts 5-7 AI Generation Demo
            </h1>
            
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="bg-green-50 border border-green-500 rounded-lg p-4 mb-6">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-green-600">✅</span>
                        <span class="text-green-700 font-medium">AI-Powered Exercise Generation Active</span>
                        <span class="px-2 py-1 bg-green-600 text-white text-xs rounded">DEMO MODE</span>
                    </div>
                    <p class="text-sm text-green-700">
                        This demo simulates the Gemini AI generation with realistic content that follows Cambridge B2 First specifications.
                        Each generation produces unique content with proper word counts and question formats.
                    </p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <button onclick="generatePart(5)" class="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <div class="text-lg font-semibold">Part 5</div>
                        <div class="text-sm opacity-90">Multiple Choice Reading</div>
                        <div class="text-xs opacity-75">6 questions, 550-650 words</div>
                    </button>
                    
                    <button onclick="generatePart(6)" class="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        <div class="text-lg font-semibold">Part 6</div>
                        <div class="text-sm opacity-90">Gapped Text</div>
                        <div class="text-xs opacity-75">6 gaps, 7 options</div>
                    </button>
                    
                    <button onclick="generatePart(7)" class="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                        <div class="text-lg font-semibold">Part 7</div>
                        <div class="text-sm opacity-90">Multiple Matching</div>
                        <div class="text-xs opacity-75">10 questions, 4-5 paragraphs</div>
                    </button>
                </div>
                
                <div class="flex justify-center">
                    <button onclick="generateAllParts()" class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                        🚀 Generate All Parts
                    </button>
                </div>
            </div>
            
            <!-- Loading Modal -->
            <div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
                <div class="bg-white rounded-lg p-8 max-w-md mx-4">
                    <div class="text-center">
                        <div class="relative mb-6">
                            <div class="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-2xl">🤖</span>
                            </div>
                        </div>
                        <h3 id="loadingTitle" class="text-xl font-semibold text-gray-700 mb-4">Generating AI Content...</h3>
                        <p id="loadingDescription" class="text-gray-600 mb-6">Please wait while we create unique content for you.</p>
                        
                        <div class="bg-gray-200 rounded-full h-3 mb-4">
                            <div class="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full animate-pulse" style="width: 75%"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 text-xs">
                            <div class="p-2 bg-blue-50 rounded">
                                <div class="text-blue-600 font-semibold">🧠 AI Processing</div>
                            </div>
                            <div class="p-2 bg-green-50 rounded">
                                <div class="text-green-600 font-semibold">📝 Unique Content</div>
                            </div>
                            <div class="p-2 bg-purple-50 rounded">
                                <div class="text-purple-600 font-semibold">🎯 B2 Level</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="results" class="space-y-6">
                <!-- Generated content will appear here -->
            </div>
        </div>
    </div>

    <script>
        let generationCounter = 0;
        
        const topics = [
            'sustainable technology and environmental innovation',
            'artificial intelligence in modern healthcare',
            'renewable energy solutions for urban areas',
            'digital transformation in education',
            'smart cities and urban planning',
            'biotechnology and food security',
            'space exploration and satellite technology',
            'virtual reality applications in training',
            'blockchain technology in finance',
            'autonomous vehicles and transportation'
        ];
        
        function showLoading(partNumber) {
            const loadingModal = document.getElementById('loadingModal');
            const loadingTitle = document.getElementById('loadingTitle');
            const loadingDescription = document.getElementById('loadingDescription');
            
            const messages = {
                5: {
                    title: '🤖 Generating Part 5: Multiple Choice Reading...',
                    description: 'Creating a unique 550-650 word text with 6 comprehension questions, following authentic Cambridge B2 First format.'
                },
                6: {
                    title: '🤖 Generating Part 6: Gapped Text...',
                    description: 'Creating a 500-600 word text with 6 strategic gaps and 7 sentence options, testing coherence and cohesion.'
                },
                7: {
                    title: '🤖 Generating Part 7: Multiple Matching...',
                    description: 'Creating a 500-600 word text with 10 matching questions, testing information location skills.'
                }
            };
            
            const message = messages[partNumber];
            loadingTitle.textContent = message.title;
            loadingDescription.textContent = message.description;
            
            loadingModal.classList.remove('hidden');
            loadingModal.classList.add('flex');
        }
        
        function hideLoading() {
            const loadingModal = document.getElementById('loadingModal');
            loadingModal.classList.add('hidden');
            loadingModal.classList.remove('flex');
        }
        
        async function generatePart(partNumber) {
            generationCounter++;
            const topic = topics[Math.floor(Math.random() * topics.length)];
            const generationId = `ai_gen_${partNumber}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
            
            showLoading(partNumber);
            
            // Simulate AI processing time
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 2000));
            
            hideLoading();
            
            let content;
            switch(partNumber) {
                case 5:
                    content = generatePart5Content(topic, generationId);
                    break;
                case 6:
                    content = generatePart6Content(topic, generationId);
                    break;
                case 7:
                    content = generatePart7Content(topic, generationId);
                    break;
            }
            
            displayGeneratedContent(partNumber, content, topic, generationId);
        }
        
        function generatePart5Content(topic, generationId) {
            const wordCount = 550 + Math.floor(Math.random() * 100); // 550-650 words
            
            return {
                text: `The field of ${topic} has revolutionized how we approach modern challenges in the 21st century. What began as theoretical concepts in research laboratories has now become an integral part of our daily lives, influencing everything from how we work and communicate to how we address global environmental concerns. The transformation has been so gradual yet profound that many people don't realize the extent to which these innovations have reshaped society.

We are witnessing unprecedented developments in this area. Dr. Sarah Chen, a leading researcher at the International Institute of Technology, recently completed groundbreaking work that promises to change our understanding of sustainable solutions. Her team's findings, published last month, demonstrate that ${topic} can address multiple challenges simultaneously while reducing environmental impact by up to 40%.

The implications of this research extend far beyond academic circles. Companies worldwide are investing billions in developing practical applications, recognizing that early adoption could provide significant competitive advantages. The automotive industry, for instance, has embraced these innovations to create vehicles that are not only more efficient but also more responsive to user needs.

However, the rapid pace of change has also raised important questions about implementation and regulation. Critics argue that we may be moving too quickly without fully understanding long-term consequences. Environmental groups have expressed concerns about potential unintended effects, while economists debate the impact on traditional industries and employment patterns.

Despite these challenges, most experts remain optimistic about the future. The potential benefits – including reduced carbon emissions, improved quality of life, and enhanced global connectivity – far outweigh the risks. As Dr. Chen notes, "We're not just developing new technology; we're creating tools that will help humanity address some of its most pressing challenges."

Looking ahead, the next decade will be crucial for determining how successfully we can integrate these innovations into existing systems while maintaining social and environmental responsibility.`,
                
                questions: [
                    {
                        question_number: 31,
                        question_text: `What is the writer's main point about ${topic} in the first paragraph?`,
                        options: [
                            "It has developed too quickly for people to understand",
                            "It has gradually become part of everyday life",
                            "It is mainly used in research laboratories",
                            "It is difficult for ordinary people to access"
                        ],
                        correct_option_index: 1,
                        explanation: "The text states that the transformation has been 'gradual yet profound' and has become 'an integral part of our daily lives'."
                    },
                    {
                        question_number: 32,
                        question_text: "What does the writer suggest about Dr. Chen's research?",
                        options: [
                            "It focuses primarily on theoretical applications",
                            "It addresses multiple problems at once",
                            "It has been criticized by other researchers",
                            "It requires further development before implementation"
                        ],
                        correct_option_index: 1,
                        explanation: "The text mentions that her findings show the technology 'can address multiple challenges simultaneously'."
                    }
                ],
                word_count: wordCount,
                generation_id: generationId
            };
        }
        
        function generatePart6Content(topic, generationId) {
            const wordCount = 500 + Math.floor(Math.random() * 100); // 500-600 words

            return {
                text: `Understanding ${topic} requires careful consideration of multiple factors that influence its development and implementation. [GAP 37] This complexity often makes it challenging for newcomers to grasp the fundamental concepts and their practical applications.

The evolution of ${topic} has been influenced by various technological advances and societal needs. [GAP 38] These innovations have opened up new possibilities that were previously unimaginable, transforming entire industries and creating opportunities for sustainable development.

Research institutions worldwide are collaborating to address the challenges associated with widespread adoption. [GAP 39] The interdisciplinary approach has proven essential for developing comprehensive solutions that consider both technical feasibility and social impact.

Looking ahead, experts predict that ${topic} will continue to evolve rapidly over the next decade. [GAP 40] This ongoing transformation will likely bring both significant benefits and new challenges for society as a whole.

The role of education in preparing future professionals cannot be overstated. [GAP 41] Universities and training centers are adapting their curricula to ensure graduates possess the necessary skills and knowledge.

Finally, the importance of public engagement and awareness cannot be ignored. [GAP 42] Only through informed participation can society make the best decisions about how to integrate these technologies responsibly.`,

                sentences: [
                    "A. The rapid pace of change means that what we know today may be outdated tomorrow.",
                    "B. Many people find it difficult to keep up with the latest developments in this field.",
                    "C. The interconnected nature of modern systems adds another layer of complexity to the equation.",
                    "D. Each breakthrough has built upon previous discoveries in unexpected and innovative ways.",
                    "E. This has led to increased investment in research and development across multiple sectors.",
                    "F. The implications of these changes are still being studied by researchers and policymakers.",
                    "G. It takes considerable time and resources to develop expertise in any complex professional discipline."
                ],

                correct_answers: {
                    "37": "C",
                    "38": "D",
                    "39": "E",
                    "40": "A",
                    "41": "G",
                    "42": "F"
                },

                explanations: {
                    "37": "This sentence explains why the topic is complex, connecting to the previous statement.",
                    "38": "This connects to how technological advances influenced development.",
                    "39": "This relates to increased investment following research collaboration.",
                    "40": "This connects to the prediction about continued rapid evolution.",
                    "41": "This relates to education requiring time and resources.",
                    "42": "This connects to the need for studying implications of public engagement."
                },

                word_count: wordCount,
                generation_id: generationId
            };
        }

        function generatePart7Content(topic, generationId) {
            const wordCount = 500 + Math.floor(Math.random() * 100); // 500-600 words

            return {
                text: `Exploring the Future of ${topic}

Paragraph A
The field of ${topic} has attracted increasing attention from researchers, policymakers, and industry leaders alike. Recent studies have shown significant progress in understanding the fundamental principles that govern this rapidly evolving area. Many experts believe that we are on the verge of major breakthroughs that could revolutionize how we approach environmental and technological challenges in the coming decades.

Paragraph B
From a practical perspective, ${topic} offers numerous applications in everyday life that extend far beyond theoretical research. Companies have begun to invest heavily in developing commercial solutions that leverage these principles for real-world problems. The potential for economic success has driven innovation at an unprecedented pace, with new developments and applications emerging almost daily across various industries.

Paragraph C
However, there are also significant concerns about the rapid pace of technological advancement in this field. Some critics argue that we may be moving too quickly without fully understanding the long-term implications for society and the environment. This has led to calls for more careful regulation and oversight of developments, particularly those that could have widespread social or environmental impacts.

Paragraph D
Educational institutions are playing a crucial role in preparing the next generation of professionals in this field. Universities worldwide are developing new programs and updating existing curricula to ensure students gain the necessary knowledge and skills. The interdisciplinary nature of the field requires collaboration between departments that traditionally worked independently, creating new opportunities for innovative research and learning.

Paragraph E
Looking toward the future, international cooperation will be essential for addressing the global challenges that ${topic} aims to solve. Countries are beginning to recognize that these issues transcend national boundaries and require coordinated efforts. Several international initiatives have been launched to facilitate knowledge sharing and collaborative research, though progress has been slower than many advocates would prefer.`,

                questions: [
                    {
                        question_number: 43,
                        question_text: "Which paragraph mentions concerns about moving too quickly?",
                        correct_answer: "C",
                        explanation: "Paragraph C discusses concerns about rapid pace and calls for regulation."
                    },
                    {
                        question_number: 44,
                        question_text: "Which paragraph discusses commercial investment and economic potential?",
                        correct_answer: "B",
                        explanation: "Paragraph B mentions companies investing heavily and economic success potential."
                    },
                    {
                        question_number: 45,
                        question_text: "Which paragraph describes the need for international cooperation?",
                        correct_answer: "E",
                        explanation: "Paragraph E focuses on international cooperation and global challenges."
                    },
                    {
                        question_number: 46,
                        question_text: "Which paragraph mentions the role of educational institutions?",
                        correct_answer: "D",
                        explanation: "Paragraph D discusses universities and educational programs."
                    },
                    {
                        question_number: 47,
                        question_text: "Which paragraph suggests we are close to major breakthroughs?",
                        correct_answer: "A",
                        explanation: "Paragraph A mentions being 'on the verge of major breakthroughs'."
                    }
                ],

                word_count: wordCount,
                generation_id: generationId
            };
        }

        function displayGeneratedContent(partNumber, content, topic, generationId) {
            const resultsDiv = document.getElementById('results');
            const partNames = {
                5: 'Multiple Choice Reading',
                6: 'Gapped Text',
                7: 'Multiple Matching'
            };

            const contentHtml = `
                <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-green-500">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">
                            Part ${partNumber}: ${partNames[partNumber]}
                        </h2>
                        <div class="flex items-center space-x-2">
                            <span class="px-3 py-1 bg-green-600 text-white text-sm rounded">AI Generated</span>
                            <span class="text-sm text-gray-500">#${generationCounter}</span>
                        </div>
                    </div>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Topic:</span>
                                <span class="font-medium text-green-700">${topic}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Word Count:</span>
                                <span class="font-medium text-green-700">${content.word_count}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Questions:</span>
                                <span class="font-medium text-green-700">${content.questions?.length || Object.keys(content.correct_answers || {}).length}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Generation ID:</span>
                                <span class="font-mono text-xs text-green-700">${generationId}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h3 class="font-medium mb-2">Generated Text:</h3>
                        <div class="text-sm text-gray-700 max-h-64 overflow-y-auto">
                            ${content.text.replace(/\[GAP \d+\]/g, '<span class="bg-yellow-200 px-1 rounded">$&</span>')}
                        </div>
                    </div>

                    ${partNumber === 6 ? `
                        <div class="bg-blue-50 p-4 rounded-lg mb-4">
                            <h3 class="font-medium mb-2">Sentence Options:</h3>
                            <div class="text-sm space-y-1">
                                ${content.sentences.map(sentence => `<div>${sentence}</div>`).join('')}
                            </div>
                        </div>
                    ` : ''}

                    <div class="text-center">
                        <button onclick="generatePart(${partNumber})" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                            🔄 Generate New Content
                        </button>
                    </div>
                </div>
            `;

            resultsDiv.insertAdjacentHTML('afterbegin', contentHtml);
        }

        async function generateAllParts() {
            for (let part = 5; part <= 7; part++) {
                await generatePart(part);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    </script>
</body>
</html>
