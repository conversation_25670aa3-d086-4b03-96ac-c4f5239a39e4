import React, { useState } from 'react'
import { Question } from '../../../types'
import { useTestStore } from '../../../store/useTestStore'

interface WordFormationQuestionProps {
  question: Question
}

export const WordFormationQuestion: React.FC<WordFormationQuestionProps> = ({
  question
}) => {
  const { updateUserAnswer } = useTestStore()
  const [answer, setAnswer] = useState('')

  const handleAnswerChange = (value: string) => {
    setAnswer(value)
    updateUserAnswer(question.id, value)
  }

  // Extract the keyword from question text or use a default
  const getKeyword = () => {
    // Look for keyword in the question text or use a mock keyword
    const keywordMatch = question.text.match(/Keyword:\s*(\w+)/)
    return keywordMatch ? keywordMatch[1] : 'DEVELOP'
  }

  const keyword = getKeyword()

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <div className="p-4 bg-purple-50 border border-cambridge-purple rounded-lg">
        <h4 className="text-sm font-medium text-cambridge-purple mb-2">
          Instructions:
        </h4>
        <p className="text-sm text-cambridge-gray-700">
          Read the text below. Use the word given in <strong>CAPITALS</strong> to form a word that fits in the gap. 
          There is an example at the beginning (0).
        </p>
      </div>

      {/* Question Text */}
      <div className="prose max-w-none">
        <div className="text-cambridge-gray-700 leading-relaxed text-lg">
          {question.text.replace('[GAP 1]', '_______________')}
        </div>
      </div>

      {/* Keyword and Answer Section */}
      <div className="bg-cambridge-gray-50 p-6 rounded-lg border border-cambridge-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Keyword Display */}
          <div className="text-center">
            <h4 className="text-sm font-medium text-cambridge-gray-700 mb-3">
              Word to transform:
            </h4>
            <div className="bg-cambridge-purple text-white px-6 py-4 rounded-lg text-2xl font-bold">
              {keyword}
            </div>
            <p className="text-xs text-cambridge-gray-600 mt-2">
              Use this word to form the answer
            </p>
          </div>

          {/* Answer Input */}
          <div className="text-center">
            <h4 className="text-sm font-medium text-cambridge-gray-700 mb-3">
              Your answer:
            </h4>
            <input
              type="text"
              value={answer}
              onChange={(e) => handleAnswerChange(e.target.value)}
              className="w-full px-4 py-4 text-center text-xl border-2 border-cambridge-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-cambridge-purple focus:border-transparent"
              placeholder="Enter transformed word"
              maxLength={30}
            />
            <p className="text-xs text-cambridge-gray-600 mt-2">
              {answer.length}/30 characters
            </p>
          </div>
        </div>
      </div>

      {/* Word Formation Tips */}
      <div className="p-4 bg-green-50 border border-cambridge-green rounded-lg">
        <h5 className="text-sm font-medium text-cambridge-green mb-2">
          💡 Word Formation Tips:
        </h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-cambridge-gray-700">
          <div>
            <h6 className="font-medium mb-1">Common Noun Suffixes:</h6>
            <ul className="space-y-1 text-xs">
              <li>• -tion, -sion (action, decision)</li>
              <li>• -ment (development, agreement)</li>
              <li>• -ness (happiness, darkness)</li>
              <li>• -ity (ability, possibility)</li>
            </ul>
          </div>
          <div>
            <h6 className="font-medium mb-1">Common Adjective Suffixes:</h6>
            <ul className="space-y-1 text-xs">
              <li>• -ful (helpful, useful)</li>
              <li>• -less (hopeless, careless)</li>
              <li>• -able (comfortable, suitable)</li>
              <li>• -ive (active, creative)</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Example */}
      <div className="p-4 bg-blue-50 border border-cambridge-blue rounded-lg">
        <h5 className="text-sm font-medium text-cambridge-blue mb-2">
          📝 Example:
        </h5>
        <div className="text-sm text-cambridge-gray-700">
          <p className="mb-2">
            <strong>Text:</strong> "The <u>development</u> of renewable energy is important for our future."
          </p>
          <p className="mb-2">
            <strong>Keyword:</strong> DEVELOP
          </p>
          <p>
            <strong>Answer:</strong> development (noun form needed)
          </p>
        </div>
      </div>

      {/* Answer Analysis */}
      {answer && (
        <div className="p-4 bg-cambridge-gray-50 rounded-lg border border-cambridge-gray-200">
          <h5 className="text-sm font-medium text-cambridge-gray-700 mb-2">
            Answer Analysis:
          </h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-cambridge-gray-700">Original word:</span>
              <div className="text-cambridge-purple font-medium">{keyword}</div>
            </div>
            <div>
              <span className="font-medium text-cambridge-gray-700">Your answer:</span>
              <div className="text-cambridge-blue font-medium">{answer || 'Not entered'}</div>
            </div>
            <div>
              <span className="font-medium text-cambridge-gray-700">Word type needed:</span>
              <div className="text-cambridge-green font-medium">
                {answer.endsWith('tion') || answer.endsWith('sion') || answer.endsWith('ment') || answer.endsWith('ness') || answer.endsWith('ity') 
                  ? 'Noun' 
                  : answer.endsWith('ful') || answer.endsWith('less') || answer.endsWith('able') || answer.endsWith('ive')
                  ? 'Adjective'
                  : answer.endsWith('ly')
                  ? 'Adverb'
                  : 'Check context'}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Progress Indicator */}
      <div className="flex items-center justify-between pt-4 border-t border-cambridge-gray-200">
        <div className="flex items-center space-x-2">
          {answer.trim() ? (
            <>
              <div className="w-2 h-2 bg-cambridge-green rounded-full"></div>
              <span className="text-sm text-cambridge-green">Answer provided</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-cambridge-gray-400 rounded-full"></div>
              <span className="text-sm text-cambridge-gray-500">Not answered</span>
            </>
          )}
        </div>
        
        {answer && (
          <span className="text-sm text-cambridge-gray-600">
            Answer: {answer}
          </span>
        )}
      </div>
    </div>
  )
}
