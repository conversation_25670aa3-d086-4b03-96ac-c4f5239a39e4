import { Test, TestPart, Question, TestType, TIME_LIMITS } from '../types'

// Mock data for demonstration - in real app this would call Gemini API
const mockQuestions = {
  'multiple-choice-cloze': [
    {
      text: "The internet has revolutionized the way we communicate and access information. It has made it possible for people to [GAP 1] with others from around the world instantly.",
      options: ["connect", "contact", "relate", "link"],
      correctAnswer: 0,
      explanation: "'Connect' is the most appropriate word here as it means to establish communication or a relationship with someone."
    },
    {
      text: "Social media platforms have [GAP 2] dramatically in the past decade, changing how we share information.",
      options: ["grown", "developed", "expanded", "increased"],
      correctAnswer: 1,
      explanation: "'Developed' is correct as it refers to the evolution and improvement of social media platforms."
    }
  ],
  'open-cloze': [
    {
      text: "Climate change is one of [GAP 1] most pressing issues of our time. Scientists [GAP 2] been studying this phenomenon for decades.",
      correctAnswers: ["the", "have"],
      explanation: "Gap 1 requires the definite article 'the' before superlative 'most pressing'. Gap 2 needs 'have' to form present perfect tense."
    }
  ],
  'word-formation': [
    {
      text: "The [GAP 1] of renewable energy sources is crucial for our planet's future.",
      keyword: "DEVELOP",
      correctAnswer: "development",
      explanation: "The noun form 'development' is needed here to describe the process of creating renewable energy sources."
    }
  ]
}

export const generateMockTest = async (
  testType: TestType, 
  selectedParts?: number[]
): Promise<Test> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500))

  const testId = `${testType}-${Date.now()}`
  
  let parts: TestPart[] = []

  switch (testType) {
    case 'reading-use-of-english':
      parts = generateReadingUseOfEnglishParts(selectedParts)
      break
    case 'writing':
      parts = generateWritingParts(selectedParts)
      break
    case 'listening':
      parts = generateListeningParts(selectedParts)
      break
    case 'speaking':
      parts = generateSpeakingParts(selectedParts)
      break
    default:
      throw new Error(`Unsupported test type: ${testType}`)
  }

  const totalTimeLimit = parts.reduce((total, part) => total + part.timeLimit, 0)

  return {
    id: testId,
    type: testType,
    title: getTestTitle(testType),
    parts,
    totalTimeLimit,
    currentPartIndex: 0,
    currentQuestionIndex: 0,
    isCompleted: false
  }
}

const generateReadingUseOfEnglishParts = (selectedParts?: number[]): TestPart[] => {
  const allParts = [
    {
      partNumber: 1,
      title: "Multiple Choice Cloze",
      description: "Choose the correct word for each gap",
      timeLimit: 10,
      instructions: "For questions 1-8, read the text below and decide which answer (A, B, C or D) best fits each gap.",
      questionCount: 8,
      type: 'multiple-choice-cloze' as const
    },
    {
      partNumber: 2,
      title: "Open Cloze",
      description: "Write one word for each gap",
      timeLimit: 8,
      instructions: "For questions 9-16, read the text below and think of the word which best fits each gap. Use only one word in each gap.",
      questionCount: 8,
      type: 'open-cloze' as const
    },
    {
      partNumber: 3,
      title: "Word Formation",
      description: "Use the word in capitals to form a word that fits the gap",
      timeLimit: 8,
      instructions: "For questions 17-24, read the text below. Use the word given in capitals to form a word that fits in the gap.",
      questionCount: 8,
      type: 'word-formation' as const
    },
    {
      partNumber: 4,
      title: "Key Word Transformation",
      description: "Complete the second sentence using the given word",
      timeLimit: 10,
      instructions: "For questions 25-30, complete the second sentence so that it has a similar meaning to the first sentence, using the word given.",
      questionCount: 6,
      type: 'key-word-transformation' as const
    },
    {
      partNumber: 5,
      title: "Multiple Choice Reading",
      description: "Read the text and answer multiple choice questions",
      timeLimit: 15,
      instructions: "Read the text and choose the correct answer for each question.",
      questionCount: 6,
      type: 'multiple-choice-reading' as const
    },
    {
      partNumber: 6,
      title: "Gapped Text",
      description: "Choose the correct sentence for each gap",
      timeLimit: 12,
      instructions: "Read the text and choose the sentence that best fits each gap.",
      questionCount: 6,
      type: 'gapped-text' as const
    },
    {
      partNumber: 7,
      title: "Multiple Matching",
      description: "Match the questions to the correct text",
      timeLimit: 12,
      instructions: "Match each question to the correct text section.",
      questionCount: 10,
      type: 'multiple-matching' as const
    }
  ]

  const partsToGenerate = selectedParts && selectedParts.length > 0 
    ? allParts.filter(part => selectedParts.includes(part.partNumber))
    : allParts

  return partsToGenerate.map(partConfig => ({
    partNumber: partConfig.partNumber,
    title: partConfig.title,
    description: partConfig.description,
    timeLimit: partConfig.timeLimit,
    instructions: partConfig.instructions,
    questions: generateQuestionsForPart(partConfig.type, partConfig.questionCount, partConfig.partNumber)
  }))
}

const generateWritingParts = (selectedParts?: number[]): TestPart[] => {
  const allParts = [
    {
      partNumber: 1,
      title: "Essay",
      description: "Write an essay on the given topic",
      timeLimit: 40,
      instructions: "Write an essay of 140-190 words using all the notes provided.",
      questionCount: 1,
      type: 'essay' as const
    },
    {
      partNumber: 2,
      title: "Writing Task",
      description: "Choose one writing task from the options",
      timeLimit: 40,
      instructions: "Choose one of the following tasks and write 140-190 words.",
      questionCount: 1,
      type: 'writing-task' as const
    }
  ]

  const partsToGenerate = selectedParts && selectedParts.length > 0 
    ? allParts.filter(part => selectedParts.includes(part.partNumber))
    : allParts

  return partsToGenerate.map(partConfig => ({
    partNumber: partConfig.partNumber,
    title: partConfig.title,
    description: partConfig.description,
    timeLimit: partConfig.timeLimit,
    instructions: partConfig.instructions,
    questions: generateQuestionsForPart(partConfig.type, partConfig.questionCount, partConfig.partNumber)
  }))
}

const generateListeningParts = (selectedParts?: number[]): TestPart[] => {
  // Similar structure for listening parts
  return []
}

const generateSpeakingParts = (selectedParts?: number[]): TestPart[] => {
  // Similar structure for speaking parts
  return []
}

const generateQuestionsForPart = (type: string, count: number, partNumber: number): Question[] => {
  const questions: Question[] = []
  
  for (let i = 0; i < count; i++) {
    const questionId = `${type}-${partNumber}-${i + 1}`
    
    // Generate mock question based on type
    let question: Question
    
    switch (type) {
      case 'multiple-choice-cloze':
        const mcqData = mockQuestions['multiple-choice-cloze'][i % mockQuestions['multiple-choice-cloze'].length]
        question = {
          id: questionId,
          type: 'multiple-choice-cloze',
          partNumber,
          questionNumber: i + 1,
          text: mcqData.text,
          options: mcqData.options,
          correctAnswer: mcqData.correctAnswer,
          explanation: mcqData.explanation,
          isAttempted: false,
          isFlagged: false
        }
        break
        
      case 'essay':
        question = {
          id: questionId,
          type: 'essay',
          partNumber,
          questionNumber: 1,
          text: "Write an essay discussing whether technology has made our lives better or worse. Use the following notes to guide your essay: 1) Communication, 2) Work and productivity, 3) Your own idea",
          isAttempted: false,
          isFlagged: false
        }
        break
        
      default:
        question = {
          id: questionId,
          type: type as any,
          partNumber,
          questionNumber: i + 1,
          text: `Sample question ${i + 1} for ${type}`,
          isAttempted: false,
          isFlagged: false
        }
    }
    
    questions.push(question)
  }
  
  return questions
}

const getTestTitle = (testType: TestType): string => {
  switch (testType) {
    case 'reading-use-of-english':
      return 'Reading and Use of English'
    case 'writing':
      return 'Writing'
    case 'listening':
      return 'Listening'
    case 'speaking':
      return 'Speaking'
    case 'full-test':
      return 'Cambridge B2 First - Full Test'
    default:
      return 'Cambridge B2 First Test'
  }
}
