import React, { useState, useEffect } from 'react'
import { Question } from '../../../types'
import { useTestStore } from '../../../store/useTestStore'

interface EssayQuestionProps {
  question: Question
}

export const EssayQuestion: React.FC<EssayQuestionProps> = ({
  question
}) => {
  const { updateUserAnswer } = useTestStore()
  const [essay, setEssay] = useState('')
  const [wordCount, setWordCount] = useState(0)

  const minWords = 140
  const maxWords = 190

  useEffect(() => {
    const words = essay.trim().split(/\s+/).filter(word => word.length > 0)
    setWordCount(words.length)
  }, [essay])

  const handleEssayChange = (value: string) => {
    setEssay(value)
    updateUserAnswer(question.id, value)
  }

  const getWordCountStatus = () => {
    if (wordCount < minWords) return 'under'
    if (wordCount > maxWords) return 'over'
    return 'good'
  }

  const wordCountStatus = getWordCountStatus()

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <div className="p-4 bg-green-50 border border-cambridge-green rounded-lg">
        <h4 className="text-sm font-medium text-cambridge-green mb-2">
          Instructions:
        </h4>
        <p className="text-sm text-cambridge-gray-700 mb-2">
          Write an essay of <strong>140-190 words</strong> using all the notes provided. 
          Give reasons for your point of view.
        </p>
        <p className="text-xs text-cambridge-gray-600">
          Remember to organize your essay with clear paragraphs and use appropriate linking words.
        </p>
      </div>

      {/* Essay Prompt */}
      <div className="prose max-w-none">
        <div className="p-6 bg-white border-2 border-cambridge-gray-200 rounded-lg">
          <h3 className="text-lg font-semibold text-cambridge-gray-700 mb-4">
            Essay Topic:
          </h3>
          <div className="text-cambridge-gray-700 leading-relaxed">
            {question.text}
          </div>
        </div>
      </div>

      {/* Writing Area */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-cambridge-gray-700">
            Your Essay:
          </h4>
          <div className={`text-sm font-medium ${
            wordCountStatus === 'under' 
              ? 'text-cambridge-orange' 
              : wordCountStatus === 'over'
              ? 'text-cambridge-red'
              : 'text-cambridge-green'
          }`}>
            {wordCount} / {minWords}-{maxWords} words
          </div>
        </div>

        <textarea
          value={essay}
          onChange={(e) => handleEssayChange(e.target.value)}
          className="w-full h-96 p-4 border-2 border-cambridge-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-cambridge-green focus:border-transparent resize-none"
          placeholder="Start writing your essay here..."
        />

        {/* Word Count Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-cambridge-gray-600">
            <span>0</span>
            <span>{minWords}</span>
            <span>{maxWords}</span>
            <span>250+</span>
          </div>
          <div className="w-full bg-cambridge-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                wordCountStatus === 'under' 
                  ? 'bg-cambridge-orange' 
                  : wordCountStatus === 'over'
                  ? 'bg-cambridge-red'
                  : 'bg-cambridge-green'
              }`}
              style={{ 
                width: `${Math.min(100, (wordCount / maxWords) * 100)}%` 
              }}
            />
          </div>
        </div>
      </div>

      {/* Writing Tips */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 bg-blue-50 border border-cambridge-blue rounded-lg">
          <h5 className="text-sm font-medium text-cambridge-blue mb-2">
            📝 Essay Structure:
          </h5>
          <ul className="text-sm text-cambridge-gray-700 space-y-1">
            <li>• <strong>Introduction:</strong> State your opinion clearly</li>
            <li>• <strong>Body:</strong> Use the given notes as main points</li>
            <li>• <strong>Conclusion:</strong> Summarize your view</li>
            <li>• <strong>Linking:</strong> Use connecting words</li>
          </ul>
        </div>

        <div className="p-4 bg-purple-50 border border-cambridge-purple rounded-lg">
          <h5 className="text-sm font-medium text-cambridge-purple mb-2">
            🔗 Useful Linking Words:
          </h5>
          <ul className="text-sm text-cambridge-gray-700 space-y-1">
            <li>• <strong>Adding:</strong> Furthermore, Moreover, In addition</li>
            <li>• <strong>Contrasting:</strong> However, On the other hand</li>
            <li>• <strong>Concluding:</strong> In conclusion, To sum up</li>
            <li>• <strong>Examples:</strong> For instance, Such as</li>
          </ul>
        </div>
      </div>

      {/* Essay Analysis */}
      {essay && (
        <div className="p-4 bg-cambridge-gray-50 rounded-lg border border-cambridge-gray-200">
          <h5 className="text-sm font-medium text-cambridge-gray-700 mb-3">
            Essay Analysis:
          </h5>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                wordCountStatus === 'good' ? 'text-cambridge-green' : 'text-cambridge-orange'
              }`}>
                {wordCount}
              </div>
              <div className="text-cambridge-gray-600">Words</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-cambridge-blue">
                {essay.split('\n\n').filter(p => p.trim()).length}
              </div>
              <div className="text-cambridge-gray-600">Paragraphs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-cambridge-purple">
                {essay.split(/[.!?]+/).filter(s => s.trim()).length}
              </div>
              <div className="text-cambridge-gray-600">Sentences</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-cambridge-orange">
                {essay.length}
              </div>
              <div className="text-cambridge-gray-600">Characters</div>
            </div>
          </div>
        </div>
      )}

      {/* Word Count Warning */}
      {wordCountStatus !== 'good' && (
        <div className={`p-3 rounded-lg border ${
          wordCountStatus === 'under'
            ? 'bg-orange-50 border-cambridge-orange'
            : 'bg-red-50 border-cambridge-red'
        }`}>
          <div className="flex items-center space-x-2">
            <span className={`text-sm font-medium ${
              wordCountStatus === 'under' ? 'text-cambridge-orange' : 'text-cambridge-red'
            }`}>
              {wordCountStatus === 'under' 
                ? `⚠️ You need ${minWords - wordCount} more words`
                : `⚠️ You have ${wordCount - maxWords} words too many`
              }
            </span>
          </div>
          <p className={`text-xs mt-1 ${
            wordCountStatus === 'under' ? 'text-cambridge-orange' : 'text-cambridge-red'
          }`}>
            {wordCountStatus === 'under'
              ? 'Essays under 140 words may lose marks for insufficient content.'
              : 'Essays over 190 words may lose marks for not following instructions.'
            }
          </p>
        </div>
      )}

      {/* Progress Indicator */}
      <div className="flex items-center justify-between pt-4 border-t border-cambridge-gray-200">
        <div className="flex items-center space-x-2">
          {essay.trim() ? (
            <>
              <div className={`w-2 h-2 rounded-full ${
                wordCountStatus === 'good' ? 'bg-cambridge-green' : 'bg-cambridge-orange'
              }`}></div>
              <span className={`text-sm ${
                wordCountStatus === 'good' ? 'text-cambridge-green' : 'text-cambridge-orange'
              }`}>
                {wordCountStatus === 'good' ? 'Essay completed' : 'In progress'}
              </span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-cambridge-gray-400 rounded-full"></div>
              <span className="text-sm text-cambridge-gray-500">Not started</span>
            </>
          )}
        </div>
        
        <span className="text-sm text-cambridge-gray-600">
          {Math.round((wordCount / maxWords) * 100)}% complete
        </span>
      </div>
    </div>
  )
}
