===== INSPERA PLATFORM DESCRIPTION =====

## General Interface Layout

The Inspera platform used for Cambridge B2 First tests presents a clean, modern web interface with the following key elements:

1. **Header Area**
   - Cambridge English logo in the top left corner
   - Candidate ID displayed prominently
   - Audio status indicator (for listening tests)
   - WiFi/connection status indicator
   - Notification bell icon
   - Menu button (hamburger icon)
   - User profile/settings button

2. **Main Content Area**
   - Test instructions and questions displayed in the center
   - Clear section headings and question numbering
   - Visual separation between questions with borders/spacing
   - Answer input areas clearly distinguished from question text

3. **Navigation Panel**
   - Located at the bottom of the screen
   - Shows all parts of the test as numbered buttons
   - Displays progress within each part (e.g., "Part 1. 0 of 8 questions attempted")
   - Current part is highlighted
   - Questions within the current part are shown as numbered buttons
   - "Review your answers" button at the end

4. **Control Buttons**
   - Previous/Next navigation arrows to move between questions
   - Flag question button to mark questions for review
   - Play button for audio in listening tests

## Question Types and Interaction Methods

1. **Multiple Choice Questions**
   - Radio button selection (circular buttons)
   - Each option has a colored border (blue, purple, orange)
   - Clicking either the radio button or the text label selects the answer
   - Selected answers show a filled radio button

2. **Text Entry Fields**
   - Clear rectangular input areas for typing responses
   - Word count displayed (for writing tasks)
   - Standard text editing capabilities

3. **Dropdown Menus**
   - Used for selecting options in some question types
   - Shows currently selected option with dropdown arrow

4. **Audio Controls**
   - Play button to start audio (listening tests)
   - Audio status indicator shows "Audio is playing"
   - No pause or rewind functionality during the test

## Navigation and Progress Tracking

1. **Part Navigation**
   - Parts are clearly numbered and labeled (e.g., "Part 1", "Part 2")
   - Progress tracking shows attempted vs. total questions
   - Current part is highlighted

2. **Question Navigation**
   - Questions are numbered sequentially within each part
   - Current question is highlighted as "Active"
   - Previous/Next buttons allow sequential movement
   - Direct access to any question via numbered buttons

3. **Progress Indicators**
   - "Not attempted" label for unanswered questions
   - Visual distinction between attempted and unattempted questions
   - Review feature to check all answers before submission

## Visual Design Elements

1. **Color Scheme**
   - White background for main content area
   - Light green for correct answer indicators
   - Blue, purple, and orange borders for multiple choice options
   - Red for important notifications or flags

2. **Typography**
   - Clean, sans-serif font for readability
   - Clear hierarchy with different text sizes for headings and content
   - Bold text for emphasis and question numbers

3. **Layout**
   - Single question displayed per screen
   - Clear visual separation between question elements
   - Consistent spacing and alignment

## User Experience Flow

1. **Test Initiation**
   - Welcome screen with test title and basic instructions
   - Play button to begin (for listening tests)

2. **Question Answering**
   - Read question text and instructions
   - Select or input answer
   - Option to flag question for review
   - Navigate to next question

3. **Part Completion**
   - Progress tracking updates as questions are answered
   - Option to review all questions in the part
   - Move to next part when ready

4. **Test Completion**
   - Review all answers before final submission
   - Submit test when all questions are answered

This platform design prioritizes clarity, ease of navigation, and focus on the current task, minimizing distractions while providing clear progress tracking throughout the test experience.
