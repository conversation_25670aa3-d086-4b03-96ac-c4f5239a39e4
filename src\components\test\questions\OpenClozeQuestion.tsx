import React, { useState } from 'react'
import { Question } from '../../../types'
import { useTestStore } from '../../../store/useTestStore'

interface OpenClozeQuestionProps {
  question: Question
}

export const OpenClozeQuestion: React.FC<OpenClozeQuestionProps> = ({
  question
}) => {
  const { updateUserAnswer } = useTestStore()
  const [answers, setAnswers] = useState<{ [key: number]: string }>({})

  // Extract gaps from question text
  const gaps = question.text.match(/\[GAP \d+\]/g) || []
  const gapCount = gaps.length

  const handleAnswerChange = (gapNumber: number, value: string) => {
    const newAnswers = { ...answers, [gapNumber]: value }
    setAnswers(newAnswers)
    
    // Update the store with all answers as a JSON string
    updateUserAnswer(question.id, JSON.stringify(newAnswers))
  }

  const renderTextWithInputs = () => {
    let gapCounter = 1
    
    return question.text.split(/(\[GAP \d+\])/).map((part, index) => {
      const gapMatch = part.match(/\[GAP (\d+)\]/)
      
      if (gapMatch) {
        const gapNumber = parseInt(gapMatch[1])
        const inputId = `gap-${question.id}-${gapNumber}`
        
        return (
          <span key={index} className="inline-block mx-1">
            <input
              id={inputId}
              type="text"
              value={answers[gapNumber] || ''}
              onChange={(e) => handleAnswerChange(gapNumber, e.target.value)}
              className="inline-block w-24 px-2 py-1 text-center border-b-2 border-cambridge-blue bg-transparent focus:outline-none focus:border-cambridge-blue focus:bg-blue-50 transition-colors"
              placeholder={`${gapNumber}`}
              maxLength={20}
            />
          </span>
        )
      }
      
      return <span key={index}>{part}</span>
    })
  }

  const getAnsweredCount = () => {
    return Object.values(answers).filter(answer => answer.trim() !== '').length
  }

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <div className="p-4 bg-blue-50 border border-cambridge-blue rounded-lg">
        <h4 className="text-sm font-medium text-cambridge-blue mb-2">
          Instructions:
        </h4>
        <p className="text-sm text-cambridge-gray-700">
          Read the text below and think of the word which best fits each gap. 
          Use only <strong>one word</strong> in each gap.
        </p>
      </div>

      {/* Question Text with Input Fields */}
      <div className="prose max-w-none">
        <div className="text-cambridge-gray-700 leading-relaxed text-lg">
          {renderTextWithInputs()}
        </div>
      </div>

      {/* Gap Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-cambridge-gray-50 rounded-lg">
        <div className="text-center">
          <div className="text-2xl font-bold text-cambridge-blue">{gapCount}</div>
          <div className="text-sm text-cambridge-gray-600">Total Gaps</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-cambridge-green">{getAnsweredCount()}</div>
          <div className="text-sm text-cambridge-gray-600">Completed</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-cambridge-orange">{gapCount - getAnsweredCount()}</div>
          <div className="text-sm text-cambridge-gray-600">Remaining</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-cambridge-purple">
            {Math.round((getAnsweredCount() / gapCount) * 100)}%
          </div>
          <div className="text-sm text-cambridge-gray-600">Progress</div>
        </div>
      </div>

      {/* Individual Gap Review */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-cambridge-gray-700">
          Your Answers:
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {Array.from({ length: gapCount }, (_, index) => {
            const gapNumber = index + 1
            const answer = answers[gapNumber] || ''
            
            return (
              <div 
                key={gapNumber}
                className="flex items-center space-x-3 p-3 border border-cambridge-gray-200 rounded-lg"
              >
                <span className="text-sm font-medium text-cambridge-gray-700 min-w-[60px]">
                  Gap {gapNumber}:
                </span>
                <input
                  type="text"
                  value={answer}
                  onChange={(e) => handleAnswerChange(gapNumber, e.target.value)}
                  className="flex-1 px-3 py-1 border border-cambridge-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-cambridge-blue focus:border-transparent"
                  placeholder="Enter one word"
                  maxLength={20}
                />
                <div className="flex items-center">
                  {answer.trim() ? (
                    <div className="w-2 h-2 bg-cambridge-green rounded-full"></div>
                  ) : (
                    <div className="w-2 h-2 bg-cambridge-gray-300 rounded-full"></div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Tips */}
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h5 className="text-sm font-medium text-yellow-800 mb-2">
          💡 Tips for Open Cloze:
        </h5>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• Look for grammatical clues (articles, prepositions, pronouns)</li>
          <li>• Consider the context and meaning of the whole sentence</li>
          <li>• Common words include: the, a, an, to, of, in, on, at, for, with, by</li>
          <li>• Check if the word fits grammatically and makes sense</li>
        </ul>
      </div>

      {/* Progress Indicator */}
      <div className="flex items-center justify-between pt-4 border-t border-cambridge-gray-200">
        <div className="flex items-center space-x-2">
          {getAnsweredCount() === gapCount ? (
            <>
              <div className="w-2 h-2 bg-cambridge-green rounded-full"></div>
              <span className="text-sm text-cambridge-green">All gaps completed</span>
            </>
          ) : getAnsweredCount() > 0 ? (
            <>
              <div className="w-2 h-2 bg-cambridge-orange rounded-full"></div>
              <span className="text-sm text-cambridge-orange">
                {getAnsweredCount()} of {gapCount} completed
              </span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-cambridge-gray-400 rounded-full"></div>
              <span className="text-sm text-cambridge-gray-500">Not started</span>
            </>
          )}
        </div>
        
        <div className="w-32 bg-cambridge-gray-200 rounded-full h-2">
          <div 
            className="bg-cambridge-blue h-2 rounded-full transition-all duration-300"
            style={{ width: `${(getAnsweredCount() / gapCount) * 100}%` }}
          />
        </div>
      </div>
    </div>
  )
}
