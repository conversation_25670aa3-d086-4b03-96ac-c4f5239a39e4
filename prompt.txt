CHCI ABYCH SI VŽDY MOHL VYBRAT JAKY PART BUDU PROCVICOVAT A NEBO CELY TEST.

=== IMPLEMENTATION STATUS ===
✅ Requirements analyzed
✅ Project structure created
✅ React app with TypeScript and Tailwind CSS
✅ Inspera-like interface design
✅ Test selection with part-specific practice
✅ Reading & Use of English (Parts 1-4 implemented)
✅ Writing module with word count tracking
✅ Timer system (exam/practice modes)
✅ Navigation panel with progress tracking
✅ Google Gemini AI integration
✅ Backend API server
✅ Question types: Multiple Choice, Open Cloze, Word Formation, Essay
✅ Responsive design with Cambridge branding
✅ Complete documentation

🎯 READY TO USE - See README.md for setup instructions


Zde je vylepšený a komplexnější plán:

Celkový Plán: B2 First Gemini Trainer (Vylepšená Verze)

I. Přehled Projektu

Název Projektu: B2 First Gemini Trainer

Cíl Projektu: Vytvoření pokročilé webové aplikace pro komplexní
přípravu na zkoušku Cambridge B2 First. Aplikace bude využívat Google
Gemini AI pro dynamické generování autentických cvičení, poskytování
automatického hodnocení a personalizované zpětné vazby.

Klíčové Funkce:

Dynamické generování cvičení pro všechny části zkoušky (Reading & Use
of English, Writing, Listening, Speaking) pomocí Google Gemini, s
využitím vzorů z reálných testů.

Interaktivní uživatelské rozhraní věrně napodobující oficiální
Cambridge Digital B2 platformu (Inspera).

Automatické vyhodnocování pro objektivní části a AI-asistované
hodnocení pro Writing a Speaking.

Detailní zpětná vazba generovaná AI, včetně vysvětlení
správných/nesprávných odpovědí a tipů na zlepšení.

Přesné časové limity pro každou část a úlohu, s možností tréninku
s/bez časovače.

Vysvětlivky a strategické tipy pro každý typ úlohy, částečně z
handbooků a částečně generované AI.

(Volitelné pro MVP) Sledování pokroku uživatele, detailní statistiky a
historie testů.

(Volitelné pro MVP) Personalizované studijní plány na základě výkonu.

II. Technická Architektura

Frontend:

Framework: React.js s TypeScript (pro typovou bezpečnost a lepší
škálovatelnost).

Styling: Tailwind CSS (pro rychlý vývoj a snadné napodobení Inspera
designu) nebo custom CSS Modules s BEM metodologií pro přesnější
kontrolu.

State Management: Redux Toolkit (pro komplexní správu stavu napříč
aplikací) nebo Zustand (jednodušší alternativa).

Routing: React Router (pro navigaci mezi stránkami a částmi testu).

UI Komponenty: Vývoj vlastní sady komponent inspirovaných Inspera
(tlačítka, inputy, navigační prvky).

Audio Handling (Listening): Web Audio API pro přehrávání a případnou
základní manipulaci s audiem (generovaným přes TTS).

Backend:

Runtime & Framework: Node.js s Express.js (pro efektivní I/O operace a
JavaScript ekosystém) nebo Python s Flask/Django (pokud preferujete
Python pro AI).

Databáze:

Primární: PostgreSQL (pro relační data – uživatelé, testy, výsledky).

Cache: Redis (pro cachování odpovědí od Gemini, session management).

AI Integrace: Přímá komunikace s Google Gemini API (viz bod III).

Audio Processing (pokud by se generovalo audio na serveru): FFmpeg
(pro konverzi TTS výstupů).

Soubory (pro nahrávání Speaking): Multer (Node.js) nebo obdobná
knihovna pro ukládání na server/cloud storage.

API Integrace – Google Gemini:

Model: gemini-2.5-flash-preview-05-20 (nebo nejnovější vhodný model).

Endpoint: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent

API Klíč: AIzaSyDbUdjVGol-IsJ4f63JnHNsqWC5huV3iOM (bezpečně uložený v
environment proměnných na backendu).

Handling Požadavků: Backend bude zodpovědný za sestavování promptů,
odesílání požadavků, zpracování JSON odpovědí a error handling.

Deployment a Infrastruktura:

Kontejnerizace: Docker pro frontend i backend.

Hosting Platforma:

Frontend: Vercel nebo Netlify (pro snadný deployment React aplikací).

Backend & DB: Platformy jako Heroku, Render, AWS (EC2, RDS, S3),
Google Cloud Platform (Cloud Run, Cloud SQL, Cloud Storage).

CI/CD: GitHub Actions nebo GitLab CI/CD pro automatizované testování a
deployment.

Monitoring: Sentry (error tracking), Prometheus/Grafana nebo
cloud-specific monitoring nástroje.

III. Detailní AI Prompt Strategie (Kombinace a Vylepšení)

Základem budou prompty z druhého plánu, ale s důrazem na vkládání
vzorových úloh přímo do promptu, jak bylo původně požadováno, pro co
nejlepší imitaci stylu a formátu.

Obecný Vzor Promptu pro Generování Úlohy:
"Jsi expert na tvorbu úloh pro zkoušku Cambridge B2 First. Tvým úkolem
je vygenerovat úlohu pro [Část zkoušky, např. Reading and Use of
English Part 1] podle následujících specifikací. Úloha by měla
testovat [specifické dovednosti pro danou část, např. 'slovní zásobu,
kolokace, frázová slovesa a spojovací fráze'] a obtížnost by měla
odpovídat úrovni B2 CEFR. Téma úlohy by mělo být: '${topic}'. Zde je
PŘESNÝ PŘÍKLAD úlohy z oficiálního vzorového testu, který máš použít
jako REFERENCI PRO FORMÁT A STYL: \n---PŘÍKLAD ZAČÁTEK---\n[Vložit
kompletní text příkladu dané části z cambridge_tests.txt]\n---PŘÍKLAD
KONEC---\n\nNyní vygeneruj NOVOU, originální úlohu stejného typu na
téma '${topic}'. Zajisti, aby obsahovala všechny požadované prvky
(např. 8 mezer, 4 možnosti pro každou, atd.). Požadovaný výstupní
formát je JSON. Pro MCQ úlohy označ správnou odpověď (index 0-3) a
přidej stručné vysvětlení, proč je tato odpověď správná a ostatní ne,
s odkazem na relevantní část textu, pokud je to vhodné. Pro doplňovací
úlohy poskytni správnou odpověď a vysvětlení gramatiky/slovní zásoby."

Příklady Specifických Promptů (rozšíření z cambridge_b2_platform_plan.md):

Reading and Use of English - Part 1 (Multiple Choice Cloze):

Prompt (kód z druhého plánu je dobrý základ, doplnit o vzorovou úlohu):

const topic = "modern hobbies"; // Dynamicky měněné téma
const part1Prompt = `
Act as a Cambridge B2 First exam creator. Generate a Part 1 Multiple
Choice Cloze test.

SPECIFICATIONS:
- Text length: 150-200 words
- 8 gaps with 4 options each
- Topic for this task: ${topic}
- Level: B2 (upper-intermediate)
- Focus: vocabulary, collocations, phrasal verbs, linking phrases

EXAMPLE FORMAT AND STYLE REFERENCE (use as guide for structure,
question types, and phrasing):
---EXAMPLE START---
What is genealogy?

Genealogy is a branch of history. It concerns family history, [GAP 1]
than the national or world history studied at school. It doesn't
merely involve drawing a family tree, however – tracing your family
history can also [GAP 2] in learning about your roots and your
identity. The internet enables millions of people worldwide to [GAP 3]
information about their family history, without great [GAP 4].

People who research their family history often [GAP 5] that it's a
fascinating hobby which [GAP 6] a lot about where they come from and
whether they have famous ancestors. According to a survey involving
900 people who had researched their family history, the chances of
discovering a celebrity in your past are one in ten. The survey also
concluded that the [GAP 7] back you follow your family line, the more
likely you are to find a relation who was much wealthier than you are.
However, the vast majority of people who [GAP 8] in the survey
discovered they were better off than their ancestors.

ANSWER OPTIONS FOR EXAMPLE:
Question 1: [instead] [rather] [except] [sooner]
Question 2: [cause] [mean] [result] [lead]
... (doplnit všechny možnosti a správné odpovědi pro příklad)
---EXAMPLE END---

REQUIREMENTS FOR NEW TASK:
1. Create an engaging text about: ${topic}
2. Include exactly 8 gaps.
3. Each gap must have 4 plausible options.
4. Only one correct answer per gap. Indicate the correct answer index (0-3).
5. Test different vocabulary areas:
- Collocations (at least 2 gaps)
- Phrasal verbs (at least 2 gaps)
- Linking words (at least 1 gap)
- Words with similar meanings (at least 1 gap)
- Other relevant B2 vocabulary

OUTPUT FORMAT (JSON):
{
"text": "Full text with [GAP X] markers for the new task on ${topic}",
"questions": [
{
"gap_number": 1,
"options": ["new_option1", "new_option2", "new_option3", "new_option4"],
"correct_option_index": 0, // Index of the correct option
"explanation": "Brief explanation why this option is correct and
others are not, possibly referencing a grammar rule or collocation."
}
// ... 7 more questions
]
}
`;


Obdobně pro ostatní části R&UoE, Writing, Listening (transkripty),
Speaking. Vždy vložit relevantní kompletní příklad z
cambridge_tests.txt jako referenci pro formát a styl.

Prompt pro AI Hodnocení Writing (převzato a mírně upraveno):

const writingAssessmentPrompt = `
Act as an experienced Cambridge B2 First writing examiner. Your task
is to assess the following student's response.

TASK TYPE: ${taskType} // e.g., "Essay", "Article"
ORIGINAL PROMPT GIVEN TO STUDENT:
${originalPrompt} // Vložit celé zadání, které student dostal

STUDENT'S RESPONSE (word count: ${studentWordCount}):
---STUDENT TEXT START---
${studentText}
---STUDENT TEXT END---

Please evaluate the student's response based on the official Cambridge
B2 First assessment criteria:
1. **Content (Score 0-5):**
* Has the student addressed all parts of the prompt adequately?
* Are the ideas relevant to the task?
* Is the target reader fully informed?
* Provide a score and specific comments with examples from the student's text.
2. **Communicative Achievement (Score 0-5):**
* Does the writing achieve its communicative purpose (e.g., persuade,
inform, describe)?
* Is the style and register appropriate for the task and target
reader? (e.g., formal/informal, essay/article conventions)
* Does it hold the target reader's attention?
* Provide a score and specific comments with examples.
3. **Organisation (Score 0-5):**
* Is the text well-organised and coherent?
* Are ideas clearly paragraphed?
* Is there effective use of cohesive devices (linking words, pronouns, etc.)?
* Is there a clear overall structure (introduction, development, conclusion)?
* Provide a score and specific comments with examples.
4. **Language (Score 0-5):**
* What is the range of vocabulary used? Is it appropriate for B2 level?
* What is the range of grammatical structures used?
* How accurate is the grammar and vocabulary? Are there errors, and do
they impede communication?
* Provide a score and specific comments with examples of good language
use and errors.

**Overall Feedback:**
Provide a summary of the student's performance, highlighting key
strengths and areas for improvement. Suggest 2-3 specific actions the
student can take to improve their writing for this type of task.

OUTPUT FORMAT (JSON):
{
"assessment": {
"content": {
"score": YOUR_SCORE_0_TO_5,
"comments": "Your detailed comments with examples..."
},
"communicative_achievement": {
"score": YOUR_SCORE_0_TO_5,
"comments": "Your detailed comments with examples..."
},
"organisation": {
"score": YOUR_SCORE_0_TO_5,
"comments": "Your detailed comments with examples..."
},
"language": {
"score": YOUR_SCORE_0_TO_5,
"comments": "Your detailed comments with examples..."
}
},
"overall_feedback": "Your summary and suggestions...",
"estimated_band_score_if_possible": "e.g., Strong B2 / Borderline
B2/C1 / Needs improvement for B2"
}
`;
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

IV. Databázová Struktura (převzato a detailněji rozpracováno z
cambridge_b2_platform_plan.md)

-- Uživatelé
CREATE TABLE users (
id SERIAL PRIMARY KEY,
email VARCHAR(255) UNIQUE NOT NULL,
password_hash VARCHAR(255) NOT NULL,
name VARCHAR(100),
created_at TIMESTAMPTZ DEFAULT NOW(),
last_login TIMESTAMPTZ,
settings JSONB -- např. preferované téma, notifikace
);

-- Vygenerované Úlohy (pro možnost opakování stejné úlohy nebo pro analýzu)
CREATE TABLE generated_tasks (
id SERIAL PRIMARY KEY,
task_type VARCHAR(100) NOT NULL, -- např. 'reading_part1', 'writing_essay'
topic VARCHAR(255),
difficulty_level VARCHAR(10) DEFAULT 'B2',
content JSONB NOT NULL, -- Kompletní JSON s úlohou od Gemini (text,
otázky, odpovědi, vysvětlení)
source_prompt TEXT, -- Prompt použitý pro generování
gemini_model_version VARCHAR(100),
created_at TIMESTAMPTZ DEFAULT NOW(),
usage_count INTEGER DEFAULT 0
);

-- Uživatelské Pokusy o Test/Část (Session)
CREATE TABLE user_test_sessions (
id SERIAL PRIMARY KEY,
user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
test_type VARCHAR(100) NOT NULL, -- 'full_reading_use_of_english',
'single_writing_part1'
start_time TIMESTAMPTZ DEFAULT NOW(),
end_time TIMESTAMPTZ,
status VARCHAR(50) DEFAULT 'in_progress', -- 'in_progress',
'completed', 'abandoned'
time_limit_seconds INTEGER,
time_taken_seconds INTEGER
);

-- Odpovědi Uživatele na Jednotlivé Otázky/Úlohy v Rámci Session
CREATE TABLE user_session_answers (
id SERIAL PRIMARY KEY,
session_id INTEGER REFERENCES user_test_sessions(id) ON DELETE CASCADE,
generated_task_id INTEGER REFERENCES generated_tasks(id) ON DELETE SET
NULL, -- Odkaz na konkrétní vygenerovanou úlohu
question_identifier VARCHAR(100) NOT NULL, -- např.
'reading_part1_gap3' nebo 'writing_essay_content'
user_response JSONB, -- Odpověď uživatele (vybraná možnost, napsaný text)
is_correct BOOLEAN, -- Pro objektivně hodnocené
ai_assessment JSONB, -- Pro Writing/Speaking (JSON s hodnocením od Gemini)
score_awarded NUMERIC(5,2),
max_possible_score NUMERIC(5,2),
answered_at TIMESTAMPTZ DEFAULT NOW()
);

-- Agregované Výsledky a Pokrok Uživatele
CREATE TABLE user_progress_summary (
id SERIAL PRIMARY KEY,
user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
skill_area VARCHAR(100) NOT NULL, -- např. 'Reading Comprehension',
'Grammar', 'Essay Writing'
sub_skill VARCHAR(100), -- např. 'Collocations', 'Passive Voice',
'Essay Organisation'
average_score NUMERIC(5,2),
attempts_count INTEGER,
last_attempted_at TIMESTAMPTZ,
UNIQUE (user_id, skill_area, sub_skill)
);
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
SQL
IGNORE_WHEN_COPYING_END

V. Frontend Komponenty a Rozhraní (převzato a rozšířeno z
cambridge_b2_platform_plan.md)

Design bude striktně následovat popis z inspera_platform_description.txt.

App.tsx: Hlavní kontejner, routing, správa globálního stavu (uživatel,
aktuální test).

TestLayout.tsx: Komponenta obalující samotné testovací rozhraní,
včetně hlavičky (logo, ID, časovač, menu), navigačního panelu (přehled
částí a otázek, progress) a hlavní obsahové oblasti.

Header.tsx: Logo, ID testu/uživatele, audio status, WiFi status,
notifikace, menu, profil.

NavigationPanel.tsx: Zobrazuje části testu, otázky v aktuální části,
indikuje "active", "attempted", "not attempted", "flagged". Umožňuje
přímou navigaci.

QuestionArea.tsx: Zobrazuje instrukce pro část a aktuální otázku.

QuestionRenderer.tsx: Dynamicky renderuje různé typy otázek na základě
dat z generated_tasks.content.

MultipleChoiceQuestion.tsx (s kruhovými radio buttony a barevnými
okraji, jak je popsáno).

OpenClozeInput.tsx

WordFormationInput.tsx

KeyWordTransformationInput.tsx

LongTextInput.tsx (pro Writing).

AudioPlayerWrapper.tsx (pro Listening, s play tlačítkem, indikací
"Audio is playing", bez pauzy/rewind během přehrávání, ale s možností
druhého přehrání dle specifikace zkoušky).

Timer.tsx: Zobrazuje zbývající čas, poskytuje vizuální varování.
Implementace TIME_LIMITS z cambridge_b2_platform_plan.md.

FeedbackModal.tsx: Zobrazuje AI-generované hodnocení a vysvětlení.

Dashboard.tsx: Přehled testů, historie, statistiky (využití StatisticsManager).

Settings.tsx: Nastavení uživatele.

Styling (Tailwind CSS nebo CSS Modules):
Důraz na čistotu, minimalismus, jasnou hierarchii a kontrast, jak je
popsáno pro Inspera. Použití definované palety barev.

VI. Implementace Specifických Funkcí (rozšíření)

Časové Limity:

Implementace TimerManager s přesnými časy.

Možnost "Practice Mode" (bez časovače) a "Exam Mode" (s časovačem).

Automatické odevzdání po vypršení času v "Exam Mode".

AI Hodnocení:

Writing: Po odeslání textu backend zavolá Gemini s promptem
writingAssessmentPrompt. Výsledek se uloží do
user_session_answers.ai_assessment a zobrazí uživateli.

Speaking (pro verzi s nahráváním):

Uživatel nahraje audio.

Backend použije Speech-to-Text API (např. Google Cloud Speech-to-Text)
k transkripci.

Transkript se pošle Gemini s promptem podobným
writingAssessmentPrompt, ale upraveným pro mluvený projev a kritéria
Speakingu.

Reading & Listening (Vysvětlení chyb):

Po odevzdání odpovědi, pokud je nesprávná, frontend může poslat
požadavek na backend pro generování vysvětlení.

Backend: "Student odpověděl [Špatná odpověď] na otázku [Text otázky] v
kontextu [Relevantní část textu/poslechu]. Správná odpověď je [Správná
odpověď]. Stručně (max 3 věty) vysvětli, proč je [Správná odpověď]
správná a proč byla studentova odpověď nesprávná, s odkazem na klíčové
informace v kontextu."

Audio Funkčnost pro Listening:

Použití AudioPlayer třídy. Audio pro úlohy bude generováno pomocí
kvalitního TTS (např. Google Cloud Text-to-Speech) na základě
transkriptů od Gemini. TTS audio soubory mohou být generovány
on-demand a cachovány, nebo předgenerovány pro sadu úloh.

Důležité: dodržet pravidlo dvojího přehrání pro každou část Listening testu.

Progress Tracking a Statistiky:

Po každé dokončené session se aktualizuje user_progress_summary.

Dashboard zobrazí grafy pokroku, průměrná skóre podle dovedností/částí.

Využití StatisticsManager pro generování reportů a případných
studijních plánů (AI-generovaný prompt: "Na základě těchto statistik
výkonu uživatele v B2 First [JSON se statistikami], navrhni krátký
(3-5 bodů) studijní plán zaměřený na jeho nejslabší oblasti.").

Vysvětlení a Tipy:

Databáze tipů TIPS_DATABASE pro obecné rady.

Dynamické, kontextové tipy generované AI (viz AI hodnocení a vysvětlení chyb).

Před každou částí zobrazit stručný přehled formátu a co se testuje (z
handbooků).

VII. Testování a Zajištění Kvality (převzato a rozšířeno)

Jednotkové Testy (Jest/Vitest): Pro jednotlivé komponenty, utility
funkce, AI prompt formátování.

Integrační Testy: Testování interakce mezi frontendem a backendem,
volání Gemini API.

End-to-End Testy (Cypress/Playwright): Simulace kompletního průchodu
uživatele testem.

Manuální Testování Kvality AI Výstupů: Pravidelná kontrola kvality
generovaných úloh a hodnocení. Systém pro uživatele k nahlášení
problematických AI výstupů.

Performance Testování: Zátěžové testy pro backend a API.

VIII. Roadmapa a Budoucí Rozšíření (převzato a upřesněno)

Fáze 1 (MVP - Měsíce 1-4):

Reading & Use of English (všechny části) – generování úloh, zobrazení,
objektivní hodnocení.

Základní Inspera-like UI.

Časovače.

Uživatelská registrace/login.

Základní zpětná vazba (vysvětlení správných odpovědí generované AI).

Fáze 2 (Měsíce 5-8):

Writing (obě části) – generování zadání, textový editor, AI hodnocení
pomocí Gemini.

Listening (všechny části) – generování transkriptů, integrace TTS,
zobrazení úloh, objektivní hodnocení.

Vylepšení UI/UX, detailnější statistiky pokroku.

Fáze 3 (Měsíce 9-12):

Speaking (všechny části) – zobrazení úkolů, možnost nahrávání hlasu,
transkripce, AI hodnocení transkriptu.

Pokročilé statistiky a personalizované studijní plány generované AI.

Optimalizace promptů a kvality AI výstupů.

Fáze 4 (Další rozvoj):

Mobilní responzivita / PWA.

Komunitní funkce (sdílení výsledků, diskuse).

Možnost pro učitele vytvářet "třídy" a zadávat úkoly.

Adaptivní učení – AI upravuje obtížnost úloh na základě výkonu.

Tento vylepšený plán integruje detailní technické aspekty s původním
požadavkem na funkčnost a design. Klíčem bude pečlivá implementace AI
promptů a robustní backend pro jejich zpracování.


PROJDI SI SLOŽKU INFO A ZDE JSOU MATERIALY Z KTERYCH BUDES VYCHAZET