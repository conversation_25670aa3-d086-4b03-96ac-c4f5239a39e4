// Test Types
export type TestType = 'reading-use-of-english' | 'writing' | 'listening' | 'speaking' | 'full-test'

export type QuestionType = 
  | 'multiple-choice-cloze'     // Part 1
  | 'open-cloze'               // Part 2
  | 'word-formation'           // Part 3
  | 'key-word-transformation'  // Part 4
  | 'multiple-choice-reading'  // Part 5
  | 'gapped-text'             // Part 6
  | 'multiple-matching'       // Part 7
  | 'essay'                   // Writing Part 1
  | 'writing-task'            // Writing Part 2
  | 'listening-multiple-choice'
  | 'listening-sentence-completion'
  | 'listening-multiple-matching'
  | 'speaking-interview'
  | 'speaking-long-turn'
  | 'speaking-collaborative'
  | 'speaking-discussion'

export interface Question {
  id: string
  type: QuestionType
  partNumber: number
  questionNumber: number
  text: string
  options?: string[]
  correctAnswer?: string | number
  userAnswer?: string | number
  explanation?: string
  isAttempted: boolean
  isFlagged: boolean
  timeSpent?: number
}

export interface TestPart {
  partNumber: number
  title: string
  description: string
  timeLimit: number // in minutes
  questions: Question[]
  instructions: string
}

export interface Test {
  id: string
  type: TestType
  title: string
  parts: TestPart[]
  totalTimeLimit: number
  currentPartIndex: number
  currentQuestionIndex: number
  startTime?: Date
  endTime?: Date
  isCompleted: boolean
  score?: number
  maxScore?: number
}

export interface User {
  id: string
  email: string
  name: string
  createdAt: Date
  lastLogin?: Date
  settings: UserSettings
}

export interface UserSettings {
  preferredTheme: 'light' | 'dark'
  soundEnabled: boolean
  autoSave: boolean
  practiceMode: boolean // true = no timer, false = exam mode with timer
}

export interface TestSession {
  id: string
  userId: string
  testId: string
  startTime: Date
  endTime?: Date
  status: 'in-progress' | 'completed' | 'abandoned'
  answers: UserAnswer[]
  score?: number
  feedback?: string
}

export interface UserAnswer {
  questionId: string
  answer: string | number
  timeSpent: number
  isCorrect?: boolean
  aiAssessment?: AIAssessment
}

export interface AIAssessment {
  score: number
  maxScore: number
  feedback: string
  criteria: {
    content?: number
    communicativeAchievement?: number
    organisation?: number
    language?: number
  }
  suggestions: string[]
}

// API Types
export interface GenerateTaskRequest {
  taskType: QuestionType
  topic?: string
  difficultyLevel: 'B2'
  partNumber: number
}

export interface GenerateTaskResponse {
  task: Question | Question[]
  success: boolean
  error?: string
}

// Time limits for each part (in minutes)
export const TIME_LIMITS = {
  'reading-use-of-english': {
    total: 75,
    parts: {
      1: 10,
      2: 8,
      3: 8,
      4: 10,
      5: 15,
      6: 12,
      7: 12
    }
  },
  'writing': {
    total: 80,
    parts: {
      1: 40,
      2: 40
    }
  },
  'listening': {
    total: 40,
    parts: {
      1: 8,
      2: 10,
      3: 8,
      4: 14
    }
  },
  'speaking': {
    total: 14,
    parts: {
      1: 2,
      2: 4,
      3: 4,
      4: 4
    }
  }
} as const
