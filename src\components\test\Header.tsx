import React from 'react'
import { Wifi, WifiOff, Volume2, VolumeX, <PERSON>, <PERSON>u, User, Settings } from 'lucide-react'

interface HeaderProps {
  testTitle: string
  candidateId: string
  isAudioPlaying: boolean
  hasWifiConnection: boolean
}

export const Header: React.FC<HeaderProps> = ({
  testTitle,
  candidateId,
  isAudioPlaying,
  hasWifiConnection
}) => {
  return (
    <header className="bg-white border-b-2 border-cambridge-gray-200 px-6 py-3">
      <div className="flex items-center justify-between">
        {/* Left side - Logo and Test Info */}
        <div className="flex items-center space-x-6">
          {/* Cambridge English Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-cambridge-blue rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">CE</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-cambridge-gray-700">
                Cambridge English
              </h1>
              <p className="text-sm text-cambridge-gray-500">
                {testTitle}
              </p>
            </div>
          </div>

          {/* Candidate ID */}
          <div className="bg-cambridge-gray-100 px-4 py-2 rounded-lg">
            <span className="text-sm font-medium text-cambridge-gray-700">
              ID: {candidateId}
            </span>
          </div>
        </div>

        {/* Right side - Status indicators and controls */}
        <div className="flex items-center space-x-4">
          {/* Audio Status */}
          <div className="flex items-center space-x-2">
            {isAudioPlaying ? (
              <>
                <Volume2 className="h-5 w-5 text-cambridge-green" />
                <span className="text-sm text-cambridge-green font-medium">
                  Audio is playing
                </span>
              </>
            ) : (
              <>
                <VolumeX className="h-5 w-5 text-cambridge-gray-400" />
                <span className="text-sm text-cambridge-gray-500">
                  No audio
                </span>
              </>
            )}
          </div>

          {/* WiFi Status */}
          <div className="flex items-center space-x-2">
            {hasWifiConnection ? (
              <Wifi className="h-5 w-5 text-cambridge-green" />
            ) : (
              <WifiOff className="h-5 w-5 text-cambridge-red" />
            )}
            <span className={`text-sm ${hasWifiConnection ? 'text-cambridge-green' : 'text-cambridge-red'}`}>
              {hasWifiConnection ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          {/* Notifications */}
          <button className="p-2 hover:bg-cambridge-gray-100 rounded-lg transition-colors">
            <Bell className="h-5 w-5 text-cambridge-gray-600" />
          </button>

          {/* Menu */}
          <button className="p-2 hover:bg-cambridge-gray-100 rounded-lg transition-colors">
            <Menu className="h-5 w-5 text-cambridge-gray-600" />
          </button>

          {/* User Profile */}
          <button className="p-2 hover:bg-cambridge-gray-100 rounded-lg transition-colors">
            <User className="h-5 w-5 text-cambridge-gray-600" />
          </button>
        </div>
      </div>
    </header>
  )
}
